# 📊 Mapping Visualisasi untuk Research Questions

## 🎯 **IDENTIFIKASI MASALAH & VISUALISASI YANG RELEVAN**

---

## **1️⃣ RESEARCH QUESTION 1**
### **"Faktor-faktor apa saja yang paling signifikan dalam memprediksi risiko fatigue pada mahasiswa, dan bagaimana kontribusi individual setiap fitur dapat divalidasi melalui ablation study"**

### **🎯 VISUALISASI YANG DIBUTUHKAN:**

#### **A. Feature Importance Analysis** ⭐⭐⭐⭐⭐
- **File**: `feature_analysis.png`
- **Dari**: `bias_corrected_title_classifier.py`
- **Menjawab**: Faktor paling signifikan dalam prediksi fatigue
- **Konten**: Feature importance ranking, contribution scores

#### **B. RFE Ablation Study Results** ⭐⭐⭐⭐⭐
- **File**: `rfe_analysis_[timestamp].png`
- **Dari**: `rfe_ablation_study.py`
- **Menjawab**: Validasi kontribusi individual setiap fitur
- **Konten**: Ablation study results, feature selection process

#### **C. Feature Selection Process** ⭐⭐⭐⭐
- **File**: `feature_selection_process_[timestamp].png`
- **Dari**: `rfe_ablation_study.py`
- **Menjawab**: Proses validasi fitur secara bertahap
- **Konten**: Step-by-step feature elimination, performance curves

#### **D. Correlation Matrix** ⭐⭐⭐
- **File**: `correlation_matrix.png`
- **Dari**: `data_processor.py`
- **Menjawab**: Hubungan antar faktor prediksi
- **Konten**: Correlation coefficients between features

---

## **2️⃣ RESEARCH QUESTION 2**
### **"Bagaimana efektivitas model machine learning dalam mengklasifikasikan tingkat risiko fatigue berdasarkan data aktivitas kardiovaskular dan produktivitas"**

### **🎯 VISUALISASI YANG DIBUTUHKAN:**

#### **A. Classification Analysis** ⭐⭐⭐⭐⭐
- **File**: `classification_analysis.png`
- **Dari**: `bias_corrected_title_classifier.py`
- **Menjawab**: Efektivitas klasifikasi ML model
- **Konten**: Confusion matrix, accuracy metrics, risk distribution

#### **B. Data Overview** ⭐⭐⭐⭐
- **File**: `data_overview.png`
- **Dari**: `data_processor.py`
- **Menjawab**: Hubungan aktivitas kardiovaskular vs produktivitas
- **Konten**: Scatter plots, distribution analysis

#### **C. Regression Analysis** ⭐⭐⭐⭐
- **File**: `regression_analysis.png`
- **Dari**: `create_research_specific_visualizations.py`
- **Menjawab**: Statistical relationships dan model performance
- **Konten**: R², p-values, regression lines

#### **D. RFE Analysis** ⭐⭐⭐
- **File**: `rfe_analysis_[timestamp].png`
- **Dari**: `rfe_ablation_study.py`
- **Menjawab**: Model performance dengan different feature sets
- **Konten**: Accuracy curves, optimal feature count

---

## **3️⃣ RESEARCH QUESTION 3**
### **"Apakah analisis berbasis judul aktivitas (title-only analysis) dapat memberikan prediksi fatigue yang akurat tanpa memerlukan data kuantitatif lengkap"**

### **🎯 VISUALISASI YANG DIBUTUHKAN:**

#### **A. Feature Analysis** ⭐⭐⭐⭐⭐
- **File**: `feature_analysis.png`
- **Dari**: `bias_corrected_title_classifier.py`
- **Menjawab**: Kontribusi title-based features vs quantitative features
- **Konten**: Feature importance comparison, linguistic analysis

#### **B. Classification Analysis** ⭐⭐⭐⭐
- **File**: `classification_analysis.png`
- **Dari**: `bias_corrected_title_classifier.py`
- **Menjawab**: Akurasi prediksi title-only vs full data
- **Konten**: Performance comparison, accuracy metrics

#### **C. Bias Correction Impact** ⭐⭐⭐
- **File**: `bias_correction_impact.png`
- **Dari**: `feature_filter2.py`
- **Menjawab**: Impact of linguistic bias correction
- **Konten**: Before/after bias correction, language patterns

#### **D. Feature Filtering Analysis** ⭐⭐⭐
- **File**: `feature_filtering_analysis.png`
- **Dari**: `feature_filter2.py`
- **Menjawab**: Separation of title-based vs quantitative features
- **Konten**: Feature categorization, safety analysis

---

## **4️⃣ RESEARCH QUESTION 4**
### **"Bagaimana pola gamifikasi dan achievement rate mempengaruhi konsistensi aktivitas fisik dan risiko fatigue mahasiswa"**

### **🎯 VISUALISASI YANG DIBUTUHKAN:**

#### **A. Gamification Analysis** ⭐⭐⭐⭐⭐
- **File**: `gamification_analysis.png`
- **Dari**: `data_processor.py`
- **Menjawab**: Pola gamifikasi dan achievement rate
- **Konten**: Achievement quartiles, points distribution, balance analysis

#### **B. Intervention Effectiveness** ⭐⭐⭐⭐⭐
- **File**: `intervention_effectiveness.png`
- **Dari**: `create_research_specific_visualizations.py`
- **Menjawab**: Efektivitas gamifikasi terhadap konsistensi
- **Konten**: Achievement by activity level, consistency analysis

#### **C. Time Series Analysis** ⭐⭐⭐⭐
- **File**: `time_series_analysis.png`
- **Dari**: `data_processor.py`
- **Menjawab**: Konsistensi aktivitas dari waktu ke waktu
- **Konten**: Weekly trends, consistency patterns

#### **D. User Analysis** ⭐⭐⭐
- **File**: `user_analysis.png`
- **Dari**: `data_processor.py`
- **Menjawab**: Variasi individual dalam response terhadap gamifikasi
- **Konten**: Top performers, consistency vs achievement scatter

---

## 📋 **REKOMENDASI VISUALISASI UNTUK LAPORAN**

### **🏆 PRIORITAS TERTINGGI (Must Include - 8 Visualisasi)**

1. **`feature_analysis.png`** - Menjawab RQ1 & RQ3
2. **`rfe_analysis_[timestamp].png`** - Menjawab RQ1 & RQ2
3. **`classification_analysis.png`** - Menjawab RQ2 & RQ3
4. **`gamification_analysis.png`** - Menjawab RQ4
5. **`intervention_effectiveness.png`** - Menjawab RQ4
6. **`data_overview.png`** - Menjawab RQ2
7. **`regression_analysis.png`** - Menjawab RQ2
8. **`correlation_matrix.png`** - Menjawab RQ1

### **🎯 PRIORITAS TINGGI (Highly Recommended - 3 Visualisasi)**

9. **`feature_selection_process_[timestamp].png`** - Menjawab RQ1
10. **`time_series_analysis.png`** - Menjawab RQ4
11. **`bias_correction_impact.png`** - Menjawab RQ3

### **⚠️ PRIORITAS SEDANG (Optional - 2 Visualisasi)**

12. **`user_analysis.png`** - Menjawab RQ4
13. **`feature_filtering_analysis.png`** - Menjawab RQ3

---

## 📊 **STRUKTUR LAPORAN YANG DISARANKAN**

### **BAB HASIL (Results)**

#### **Section 4.1: Feature Significance Analysis (RQ1)**
- `feature_analysis.png` - Feature importance ranking
- `rfe_analysis_[timestamp].png` - Ablation study results
- `correlation_matrix.png` - Inter-feature relationships

#### **Section 4.2: ML Model Effectiveness (RQ2)**
- `classification_analysis.png` - Model performance
- `data_overview.png` - Data relationships
- `regression_analysis.png` - Statistical validation

#### **Section 4.3: Title-Only Analysis Validation (RQ3)**
- `feature_analysis.png` - Title vs quantitative features
- `classification_analysis.png` - Accuracy comparison
- `bias_correction_impact.png` - Linguistic bias effects

#### **Section 4.4: Gamification Impact Analysis (RQ4)**
- `gamification_analysis.png` - Achievement patterns
- `intervention_effectiveness.png` - Gamification effectiveness
- `time_series_analysis.png` - Consistency trends

---

## ✅ **KESIMPULAN**

### **Total Visualisasi untuk Laporan: 11-13 visualisasi**

- **8 visualisasi WAJIB** yang langsung menjawab research questions
- **3 visualisasi TINGGI** untuk mendukung analisis mendalam
- **2 visualisasi OPSIONAL** untuk kelengkapan

### **Semua visualisasi tersedia di**: `results/visualizations/`

### **Cara Generate**: 
```bash
python main5.py --bias-corrected-only  # → 10+ visualisasi
python src/rfe_ablation_study.py       # → 2 visualisasi RFE
```

**🎯 Dengan 11-13 visualisasi ini, semua 4 research questions dapat dijawab secara komprehensif dengan bukti visual yang kuat!**
