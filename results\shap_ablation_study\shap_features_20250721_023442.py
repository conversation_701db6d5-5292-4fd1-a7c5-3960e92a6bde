"""
SHAP-Based Optimal Features - Generated 20250721_023442
Best Algorithm: Random Forest (Accuracy: 0.9492)
"""

# All features ranked by SHAP importance
SHAP_RANKED_FEATURES = [
    "consistency_score",  # SHAP: 0.2475
    "total_cycles",  # SHAP: 0.0883
    "gamification_balance",  # SHAP: 0.0530
    "title_balance_ratio",  # SHAP: 0.0174
    "total_distance_km",  # SHAP: 0.0169
    "activity_days",  # SHAP: 0.0156
    "pomokit_title_length",  # SHAP: 0.0133
    "total_time_minutes",  # SHAP: 0.0107
    "strava_unique_words",  # SHAP: 0.0086
    "strava_title_length",  # SHAP: 0.0072
    "avg_distance_km",  # SHAP: 0.0036
    "avg_time_minutes",  # SHAP: 0.0035
    "activity_points",  # SHAP: 0.0024
    "pomokit_unique_words",  # SHAP: 0.0016
    "achievement_rate",  # SHAP: 0.0014
    "pomokit_title_count",  # SHAP: 0.0003
    "strava_title_count",  # SHAP: 0.0001
    "work_days",  # SHAP: 0.0001
    "productivity_points",  # SHAP: 0.0000
    "total_title_diversity",  # SHAP: 0.0000
]

# Top 5 SHAP features
TOP_5_SHAP_FEATURES = [
    "consistency_score",  # SHAP: 0.2475
    "total_cycles",  # SHAP: 0.0883
    "gamification_balance",  # SHAP: 0.0530
    "title_balance_ratio",  # SHAP: 0.0174
    "total_distance_km",  # SHAP: 0.0169
]

# Top 10 SHAP features
TOP_10_SHAP_FEATURES = [
    "consistency_score",  # SHAP: 0.2475
    "total_cycles",  # SHAP: 0.0883
    "gamification_balance",  # SHAP: 0.0530
    "title_balance_ratio",  # SHAP: 0.0174
    "total_distance_km",  # SHAP: 0.0169
    "activity_days",  # SHAP: 0.0156
    "pomokit_title_length",  # SHAP: 0.0133
    "total_time_minutes",  # SHAP: 0.0107
    "strava_unique_words",  # SHAP: 0.0086
    "strava_title_length",  # SHAP: 0.0072
]

# Top 15 SHAP features
TOP_15_SHAP_FEATURES = [
    "consistency_score",  # SHAP: 0.2475
    "total_cycles",  # SHAP: 0.0883
    "gamification_balance",  # SHAP: 0.0530
    "title_balance_ratio",  # SHAP: 0.0174
    "total_distance_km",  # SHAP: 0.0169
    "activity_days",  # SHAP: 0.0156
    "pomokit_title_length",  # SHAP: 0.0133
    "total_time_minutes",  # SHAP: 0.0107
    "strava_unique_words",  # SHAP: 0.0086
    "strava_title_length",  # SHAP: 0.0072
    "avg_distance_km",  # SHAP: 0.0036
    "avg_time_minutes",  # SHAP: 0.0035
    "activity_points",  # SHAP: 0.0024
    "pomokit_unique_words",  # SHAP: 0.0016
    "achievement_rate",  # SHAP: 0.0014
]

# SHAP importance scores
SHAP_IMPORTANCE_SCORES = {
    "consistency_score": 0.247525,
    "total_cycles": 0.088263,
    "gamification_balance": 0.052953,
    "title_balance_ratio": 0.017423,
    "total_distance_km": 0.016897,
    "activity_days": 0.015553,
    "pomokit_title_length": 0.013341,
    "total_time_minutes": 0.010669,
    "strava_unique_words": 0.008571,
    "strava_title_length": 0.007195,
    "avg_distance_km": 0.003551,
    "avg_time_minutes": 0.003456,
    "activity_points": 0.002430,
    "pomokit_unique_words": 0.001592,
    "achievement_rate": 0.001355,
    "pomokit_title_count": 0.000323,
    "strava_title_count": 0.000098,
    "work_days": 0.000062,
    "productivity_points": 0.000046,
    "total_title_diversity": 0.000000,
}

# Usage example:
# X_shap = X[TOP_5_SHAP_FEATURES]
# model.fit(X_shap, y)