# 📊 Dokumentasi Output main1.py - Pipeline SHAP untuk Prediksi Kelelahan

## 🎯 Gambaran Umum

`main1.py` adalah pipeline analisis komprehensif yang menggunakan **SHAP (SHapley Additive exPlanations)** untuk prediksi risiko kelelahan. Pipeline ini mengintegrasikan analisis aktivitas fisik dan produktivitas kerja dengan teknik explainable AI untuk memberikan wawasan mendalam tentang faktor-faktor yang mempengaruhi kelelahan.

## 🚀 Mode Eksekusi

Pipeline mendukung 4 mode operasi:

```bash
# Mode lengkap (default)
python main1.py

# Hanya klasifikasi kelelahan + SHAP
python main1.py --fatigue-only

# Hanya analisis ML SHAP
python main1.py --ml-only

# Hanya pemrosesan data
python main1.py --no-ml
```

## 🤖 Algoritma Machine Learning

Pipeline menguji **4 algoritma** dengan cross-validation 5-fold:

| Algoritma | Tipe | Explainer SHAP | Keterangan |
|-----------|------|----------------|------------|
| **Logistic Regression** | Linear | KernelExplainer | Model linear untuk baseline |
| **Random Forest** | Tree Ensemble | KernelExplainer | Ensemble decision trees |
| **Gradient Boosting** | Tree Ensemble | KernelExplainer | Sequential boosting |
| **XGBoost** | Tree Ensemble | KernelExplainer | Optimized gradient boosting |

## 📁 Struktur Output

### 1. **Direktori Utama**

```
results/
├── shap_ablation_study/     # Hasil analisis SHAP
├── feature_selection/       # Seleksi fitur terorganisir
├── visualizations/          # Grafik dan plot
└── reports/                 # Laporan tekstual
```

### 2. **SHAP Ablation Study** (`results/shap_ablation_study/`)

#### File Utama:
- **`shap_results_YYYYMMDD_HHMMSS.csv`** - Dataset hasil lengkap
- **`shap_report_YYYYMMDD_HHMMSS.txt`** - Laporan tekstual komprehensif
- **`shap_features_YYYYMMDD_HHMMSS.py`** - Kode Python untuk fitur optimal

#### File Per Algoritma:
- **`shap_logistic_regression_YYYYMMDD_HHMMSS.csv`** - Hasil Logistic Regression
- **`shap_random_forest_YYYYMMDD_HHMMSS.csv`** - Hasil Random Forest
- **`shap_gradient_boosting_YYYYMMDD_HHMMSS.csv`** - Hasil Gradient Boosting
- **`shap_xgboost_YYYYMMDD_HHMMSS.csv`** - Hasil XGBoost

### 3. **Feature Selection** (`results/feature_selection/`)

#### 📊 **SHAP Features** (`shap_features/`)
Fitur terbaik untuk setiap algoritma dalam berbagai jumlah:

**Format File**: `top_{N}_{algorithm}_{timestamp}.{ext}`

**Contoh**:
```
top_10_random_forest_20250721_022901.txt
top_15_logistic_regression_20250721_022901.csv
top_20_xgboost_20250721_022901.txt
```

**Isi File TXT**:
```
# Top 10 SHAP Features - Random Forest
# Generated: 20250721_022901
# Total features analyzed: 20

 1. consistency_score              (SHAP: 0.262069)
 2. work_days                      (SHAP: 0.125903)
 3. gamification_balance           (SHAP: 0.020191)
 4. total_distance_km              (SHAP: 0.014619)
 5. title_balance_ratio            (SHAP: 0.013438)
...
```

#### 🎯 **Selected Features** (`selected_features/`)
Fitur konsensus yang muncul di multiple algoritma:

**File**: `consensus_features_YYYYMMDD_HHMMSS.txt`

**Contoh Isi**:
```
# Consensus SHAP Features
# Features that appear in top 10 of multiple algorithms

consistency_score              (appears in 4 algorithms)
work_days                      (appears in 4 algorithms)
gamification_balance           (appears in 4 algorithms)
total_distance_km              (appears in 4 algorithms)
```

#### 📈 **Feature Rankings** (`feature_rankings/`)
Perbandingan peringkat fitur across algoritma:

**File**: `feature_rankings_YYYYMMDD_HHMMSS.csv`

**Kolom**:
- `feature` - Nama fitur
- `{algorithm}_rank` - Peringkat di algoritma tertentu
- `{algorithm}_importance` - Nilai SHAP importance
- `avg_rank` - Rata-rata peringkat

#### 🔢 **Feature Importance** (`feature_importance/`)
Matriks nilai SHAP untuk semua fitur dan algoritma:

**File**: `shap_importance_matrix_YYYYMMDD_HHMMSS.csv`

**Format**: Features × Algorithms matrix dengan nilai SHAP

### 4. **Visualizations** (`results/visualizations/`)

#### SHAP-Specific Plots:
- **`shap_analysis_YYYYMMDD_HHMMSS.png`** - 4-panel analisis SHAP utama
- **`cv_analysis_YYYYMMDD_HHMMSS.png`** - Analisis cross-validation
- **`shap_summary_{algorithm}_YYYYMMDD_HHMMSS.png`** - Summary plot per algoritma
- **`feature_consistency_YYYYMMDD_HHMMSS.png`** - Konsistensi fitur
- **`shap_waterfall_{algorithm}_YYYYMMDD_HHMMSS.png`** - Waterfall plot prediksi individual

#### General Visualizations:
- **`data_overview.png`** - Overview dataset
- **`correlation_matrix.png`** - Matriks korelasi
- **`time_series_analysis.png`** - Analisis time series
- **`research_specific_analysis.png`** - Analisis khusus penelitian

## 📊 Interpretasi Hasil

### 1. **Performance Metrics**

Setiap algoritma dievaluasi dengan:
- **Test Accuracy** - Akurasi pada test set
- **Test F1-Score** - F1-score pada test set  
- **CV Accuracy** - Rata-rata akurasi cross-validation ± std
- **CV F1-Score** - Rata-rata F1-score cross-validation ± std

### 2. **SHAP Feature Importance**

**Nilai SHAP** menunjukkan:
- **Magnitude**: Seberapa besar pengaruh fitur
- **Direction**: Positif (meningkatkan risiko) vs Negatif (menurunkan risiko)
- **Consistency**: Fitur yang stabil across algoritma

### 3. **Consensus Features**

Fitur yang muncul di top-N multiple algoritma menunjukkan:
- **Robustness** - Tidak bergantung pada algoritma spesifik
- **Reliability** - Konsisten dalam berbagai pendekatan
- **Production-Ready** - Cocok untuk model produksi

## 🎯 Fitur-Fitur Kunci yang Teridentifikasi

### **Top 10 Fitur Konsensus**:

1. **`consistency_score`** - Skor konsistensi aktivitas harian
2. **`work_days`** - Jumlah hari kerja dalam seminggu
3. **`gamification_balance`** - Keseimbangan gamifikasi aktivitas
4. **`total_distance_km`** - Total jarak aktivitas fisik (km)
5. **`title_balance_ratio`** - Rasio keseimbangan judul aktivitas
6. **`total_time_minutes`** - Total durasi aktivitas (menit)
7. **`strava_title_count`** - Jumlah aktivitas Strava
8. **`pomokit_title_length`** - Panjang rata-rata judul Pomokit
9. **`strava_title_length`** - Panjang rata-rata judul Strava
10. **`strava_unique_words`** - Jumlah kata unik dalam judul Strava

## 💡 Insights Bisnis

### **Faktor Kelelahan Utama**:

1. **Konsistensi > Intensitas**: `consistency_score` sebagai fitur #1 menunjukkan keteraturan lebih penting dari intensitas sesaat

2. **Work-Life Balance**: Fitur `gamification_balance` dan `title_balance_ratio` mengindikasikan pentingnya keseimbangan

3. **Aktivitas Fisik Protektif**: Fitur Strava menunjukkan olahraga berperan dalam mengurangi kelelahan

4. **Beban Kerja Linear**: `work_days` menunjukkan hubungan langsung dengan risiko kelelahan

## 🔧 Penggunaan Hasil

### **Untuk Peneliti**:
- Gunakan consensus features untuk feature selection
- Analisis SHAP values untuk interpretasi model
- Cross-validation metrics untuk validasi

### **Untuk Developer**:
- Implementasi fitur optimal dari file `.py`
- Monitoring model dengan feature importance matrix
- A/B testing dengan different feature sets

### **Untuk Stakeholder**:
- Laporan tekstual untuk executive summary
- Visualizations untuk presentasi
- Business insights untuk strategic decisions

## 📈 Contoh Penggunaan

### **Load Feature Rankings**:
```python
import pandas as pd

# Load feature rankings
rankings = pd.read_csv('results/feature_selection/feature_rankings/feature_rankings_20250721_022901.csv')

# Get top 10 consensus features
top_features = rankings.nsmallest(10, 'avg_rank')['feature'].tolist()
```

### **Load SHAP Results**:
```python
# Load SHAP results for specific algorithm
rf_results = pd.read_csv('results/shap_ablation_study/shap_random_forest_20250721_022901.csv')

# Get feature importance
importance_matrix = pd.read_csv('results/feature_selection/feature_importance/shap_importance_matrix_20250721_022901.csv')
```

## 🎉 Kesimpulan

Pipeline `main1.py` menghasilkan analisis komprehensif dengan:
- **4 algoritma ML** dengan cross-validation
- **Explainable AI** menggunakan SHAP
- **Organized feature selection** dalam folder terstruktur
- **Rich visualizations** untuk interpretasi
- **Production-ready outputs** untuk implementasi

Hasil menunjukkan bahwa **konsistensi aktivitas**, **keseimbangan work-life**, dan **aktivitas fisik** adalah faktor kunci dalam prediksi kelelahan dengan akurasi hingga **94.92%**.
