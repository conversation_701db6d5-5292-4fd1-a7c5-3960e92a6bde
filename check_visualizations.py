#!/usr/bin/env python3
"""
Check all visualization files in results/visualizations/
"""

from pathlib import Path
import os

def check_visualizations():
    """Check all visualization files"""
    print("📊 VISUALIZATION FILES CHECK")
    print("=" * 60)
    
    viz_dir = Path("results/visualizations")
    
    if not viz_dir.exists():
        print("❌ Directory does not exist: results/visualizations/")
        return
    
    print(f"📁 Directory: {viz_dir.absolute()}")
    print(f"✅ Directory exists: {viz_dir.exists()}")
    
    # Get all PNG files
    png_files = list(viz_dir.glob("*.png"))
    
    print(f"\n📊 Found {len(png_files)} visualization files:")
    print("-" * 60)
    
    total_size = 0
    
    for i, file in enumerate(sorted(png_files), 1):
        size_bytes = file.stat().st_size
        size_mb = size_bytes / (1024 * 1024)
        total_size += size_mb
        
        print(f"{i:2d}. ✅ {file.name}")
        print(f"     Size: {size_mb:.2f} MB ({size_bytes:,} bytes)")
        print(f"     Path: {file}")
        print()
    
    print("-" * 60)
    print(f"📊 SUMMARY:")
    print(f"   Total files: {len(png_files)}")
    print(f"   Total size: {total_size:.2f} MB")
    print(f"   Average size: {total_size/len(png_files):.2f} MB per file" if png_files else "   No files found")
    
    # Expected files
    expected_files = [
        'data_overview.png',
        'correlation_matrix.png', 
        'time_series_analysis.png',
        'user_analysis.png',
        'gamification_analysis.png',
        'classification_analysis.png',
        'feature_analysis.png',
        'feature_filtering_analysis.png',
        'bias_correction_impact.png'
    ]
    
    print(f"\n📋 EXPECTED FILES CHECK:")
    print("-" * 60)
    
    found_files = [f.name for f in png_files]
    
    for expected in expected_files:
        status = "✅" if expected in found_files else "❌"
        print(f"{status} {expected}")
    
    missing_files = [f for f in expected_files if f not in found_files]
    extra_files = [f for f in found_files if f not in expected_files]
    
    if missing_files:
        print(f"\n❌ Missing files ({len(missing_files)}):")
        for file in missing_files:
            print(f"   - {file}")
    
    if extra_files:
        print(f"\n➕ Extra files ({len(extra_files)}):")
        for file in extra_files:
            print(f"   + {file}")
    
    if not missing_files and not extra_files:
        print(f"\n🎉 ALL EXPECTED FILES FOUND!")
    
    print("\n" + "=" * 60)
    print("✅ VISUALIZATION CHECK COMPLETED")
    print("=" * 60)

if __name__ == "__main__":
    check_visualizations()
