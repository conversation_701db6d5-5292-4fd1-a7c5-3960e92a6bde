algorithm,algorithm_name,n_features_selected,selected_features,accuracy_mean,accuracy_std,f1_macro_mean,f1_macro_std,precision_macro_mean,recall_macro_mean,train_accuracy_mean,overfitting_score,status
logistic_regression,Logistic Regression,5,"['pomokit_title_count', 'total_cycles', 'consistency_score', 'work_days', 'title_balance_ratio']",0.5016949152542373,0.070749880902131,0.513300433559363,0.07352977627608752,0.5047824147201145,0.6139080459770115,0.507795619357703,0.006100704103465748,success
logistic_regression,Logistic Regression,10,"['pomokit_title_count', 'total_cycles', 'pomokit_title_length', 'pomokit_unique_words', 'consistency_score', 'gamification_balance', 'work_days', 'achievement_rate', 'strava_title_length', 'title_balance_ratio']",0.6189362945645821,0.07722464875420029,0.6247124656828157,0.0729636665114429,0.6177495214337319,0.666360153256705,0.7165125055498002,0.09757621098521807,success
logistic_regression,Logistic Regression,15,"['pomokit_title_count', 'productivity_points', 'total_title_diversity', 'total_cycles', 'avg_distance_km', 'pomokit_title_length', 'pomokit_unique_words', 'consistency_score', 'gamification_balance', 'work_days', 'achievement_rate', 'strava_title_length', 'strava_title_count', 'title_balance_ratio', 'total_distance_km']",0.6223845704266511,0.08207762988946464,0.6250402080769483,0.07530915299454966,0.6177316781754346,0.6622860791826308,0.7225025899067634,0.10011801948011234,success
logistic_regression,Logistic Regression,20,"['pomokit_title_count', 'productivity_points', 'total_title_diversity', 'total_cycles', 'avg_distance_km', 'pomokit_title_length', 'pomokit_unique_words', 'activity_days', 'consistency_score', 'gamification_balance', 'work_days', 'achievement_rate', 'strava_title_length', 'activity_points', 'avg_time_minutes', 'strava_unique_words', 'strava_title_count', 'title_balance_ratio', 'total_time_minutes', 'total_distance_km']",0.6154880187025131,0.07590325770579145,0.6188249718848833,0.06839030054344496,0.6122820841964288,0.6515453384418902,0.7310936806274974,0.11560566192498423,success
random_forest,Random Forest,5,"['pomokit_title_count', 'productivity_points', 'consistency_score', 'work_days', 'strava_title_count']",0.948509643483343,0.032593124501960835,0.9349161995190789,0.04207523278164914,0.9298396665063333,0.9437547892720307,0.9819483498594052,0.033438706376062166,success
random_forest,Random Forest,10,"['pomokit_title_count', 'productivity_points', 'total_cycles', 'activity_days', 'consistency_score', 'work_days', 'achievement_rate', 'strava_title_length', 'strava_title_count', 'total_distance_km']",0.9519579193454121,0.03668888160309854,0.9399677116493548,0.04769446278179122,0.9320618887285554,0.9511621966794379,0.9965665236051503,0.04460860425973823,success
random_forest,Random Forest,15,"['pomokit_title_count', 'productivity_points', 'total_title_diversity', 'total_cycles', 'pomokit_title_length', 'activity_days', 'consistency_score', 'gamification_balance', 'work_days', 'achievement_rate', 'strava_title_length', 'strava_unique_words', 'strava_title_count', 'title_balance_ratio', 'total_distance_km']",0.9451198129748685,0.03132114800113683,0.9302850814941316,0.03814101816609624,0.9272188773338199,0.937088122605364,1.0,0.05488018702513153,success
random_forest,Random Forest,20,"['pomokit_title_count', 'productivity_points', 'total_title_diversity', 'total_cycles', 'avg_distance_km', 'pomokit_title_length', 'pomokit_unique_words', 'activity_days', 'consistency_score', 'gamification_balance', 'work_days', 'achievement_rate', 'strava_title_length', 'activity_points', 'avg_time_minutes', 'strava_unique_words', 'strava_title_count', 'title_balance_ratio', 'total_time_minutes', 'total_distance_km']",0.9416715371127994,0.027730826773699337,0.9252571860851109,0.03301576495192997,0.9241792860183665,0.9296807151979566,1.0,0.058328462887200616,success
gradient_boosting,Gradient Boosting,5,"['productivity_points', 'total_cycles', 'pomokit_title_length', 'consistency_score', 'work_days']",0.9105201636469902,0.048168474886922136,0.875343700583098,0.06653370107943785,0.8961644304692136,0.8696807151979566,0.9845160574219329,0.07399589377494276,success
gradient_boosting,Gradient Boosting,10,"['pomokit_title_count', 'productivity_points', 'total_cycles', 'pomokit_title_length', 'consistency_score', 'work_days', 'strava_title_length', 'title_balance_ratio', 'total_time_minutes', 'total_distance_km']",0.9244301578024545,0.04436368087465409,0.8970604377677608,0.06413644379144644,0.904253115977254,0.8977522349936142,1.0,0.07556984219754548,success
gradient_boosting,Gradient Boosting,15,"['pomokit_title_count', 'productivity_points', 'total_title_diversity', 'total_cycles', 'avg_distance_km', 'pomokit_title_length', 'pomokit_unique_words', 'consistency_score', 'gamification_balance', 'work_days', 'strava_title_length', 'strava_title_count', 'title_balance_ratio', 'total_time_minutes', 'total_distance_km']",0.9243717124488603,0.03555063243952122,0.8976141453274649,0.0490862424959071,0.9063679903330442,0.8970114942528735,1.0,0.0756282875511397,success
gradient_boosting,Gradient Boosting,20,"['pomokit_title_count', 'productivity_points', 'total_title_diversity', 'total_cycles', 'avg_distance_km', 'pomokit_title_length', 'pomokit_unique_words', 'activity_days', 'consistency_score', 'gamification_balance', 'work_days', 'achievement_rate', 'strava_title_length', 'activity_points', 'avg_time_minutes', 'strava_unique_words', 'strava_title_count', 'title_balance_ratio', 'total_time_minutes', 'total_distance_km']",0.9243717124488603,0.040256249943055586,0.8984480649250933,0.05440645407837826,0.905054713804714,0.8970114942528735,1.0,0.0756282875511397,success
svm,Support Vector Machine,5,"['pomokit_title_count', 'productivity_points', 'total_cycles', 'consistency_score', 'work_days']",0.5154880187025132,0.026939440954482186,0.4650092244145944,0.05386382864768333,0.37797705218757843,0.669655172413793,0.5257473730945685,0.010259354392055386,success
svm,Support Vector Machine,10,"['pomokit_title_count', 'productivity_points', 'total_title_diversity', 'total_cycles', 'pomokit_title_length', 'pomokit_unique_words', 'consistency_score', 'work_days', 'strava_title_length', 'title_balance_ratio']",0.635885447106955,0.051525139986978115,0.639508540020595,0.045669974552268615,0.6433655384604151,0.7203065134099617,0.6726690839129791,0.036783636806024034,success
svm,Support Vector Machine,15,"['pomokit_title_count', 'productivity_points', 'total_title_diversity', 'total_cycles', 'avg_distance_km', 'pomokit_title_length', 'pomokit_unique_words', 'consistency_score', 'gamification_balance', 'work_days', 'achievement_rate', 'strava_title_length', 'strava_title_count', 'title_balance_ratio', 'total_distance_km']",0.6565166569257744,0.03915166947799978,0.6562403485384323,0.03717574376703878,0.6546178915884385,0.7198084291187741,0.7062009767648364,0.04968431983906196,success
svm,Support Vector Machine,20,"['pomokit_title_count', 'productivity_points', 'total_title_diversity', 'total_cycles', 'avg_distance_km', 'pomokit_title_length', 'pomokit_unique_words', 'activity_days', 'consistency_score', 'gamification_balance', 'work_days', 'achievement_rate', 'strava_title_length', 'activity_points', 'avg_time_minutes', 'strava_unique_words', 'strava_title_count', 'title_balance_ratio', 'total_time_minutes', 'total_distance_km']",0.6462887200467563,0.04974060139631366,0.6439882965086691,0.04857232810250288,0.6403438493234114,0.7075095785440614,0.7096381530264909,0.06334943297973461,success
xgboost,XGBoost,5,"['pomokit_title_count', 'productivity_points', 'total_cycles', 'activity_days', 'consistency_score']",0.9554061952074809,0.031804477167276565,0.9463846396376023,0.03796814139311865,0.9372566939233605,0.9636781609195403,0.9604817226579844,0.0050755274505034365,success
xgboost,XGBoost,10,"['pomokit_title_count', 'productivity_points', 'total_title_diversity', 'total_cycles', 'activity_days', 'consistency_score', 'gamification_balance', 'strava_title_length', 'activity_points', 'strava_title_count']",0.9244886031560491,0.03523561744838484,0.8960223525039275,0.05404009261988387,0.9067501719225858,0.898492975734355,0.9974211928370579,0.0729325896810088,success
xgboost,XGBoost,15,"['pomokit_title_count', 'productivity_points', 'total_title_diversity', 'total_cycles', 'pomokit_title_length', 'activity_days', 'consistency_score', 'gamification_balance', 'achievement_rate', 'strava_title_length', 'activity_points', 'strava_unique_words', 'strava_title_count', 'title_balance_ratio', 'total_distance_km']",0.9278199883109293,0.033459645906411074,0.9056181034166745,0.04720656114041718,0.9119540709191248,0.9084929757343551,1.0,0.07218001168907073,success
xgboost,XGBoost,20,"['pomokit_title_count', 'productivity_points', 'total_title_diversity', 'total_cycles', 'avg_distance_km', 'pomokit_title_length', 'pomokit_unique_words', 'activity_days', 'consistency_score', 'gamification_balance', 'work_days', 'achievement_rate', 'strava_title_length', 'activity_points', 'avg_time_minutes', 'strava_unique_words', 'strava_title_count', 'title_balance_ratio', 'total_time_minutes', 'total_distance_km']",0.9175920514319111,0.03974069165986022,0.8871965224976321,0.05900134764684316,0.8990720694595371,0.8826436781609196,1.0,0.08240794856808886,success
