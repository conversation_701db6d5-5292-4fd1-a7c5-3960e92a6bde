"""
Bias-Corrected Title-Only Fatigue Risk Classification System
Improved version addressing identified biases in classification
"""

import pandas as pd
import numpy as np
import logging
from pathlib import Path
from typing import Dict, List, Tuple
import re
import matplotlib
matplotlib.use('Agg')  # Use non-interactive backend
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime
import warnings

# Suppress warnings for cleaner output
warnings.filterwarnings('ignore')

# Set style for better visualizations
plt.style.use('seaborn-v0_8')
sns.set_palette("husl")

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class BiasCorrectedTitleClassifier:
    """
    Bias-corrected title-only fatigue risk classifier
    """
    
    def __init__(self):
        """Initialize the bias-corrected classifier"""
        self.stress_keywords = self._define_stress_keywords()
        self.workload_keywords = self._define_workload_keywords()
        self.negative_emotion_keywords = self._define_negative_emotion_keywords()
        self.recovery_keywords = self._define_recovery_keywords()
        self.time_pressure_keywords = self._define_time_pressure_keywords()
        self.exhaustion_keywords = self._define_exhaustion_keywords()
        
    def _define_stress_keywords(self) -> List[str]:
        """Define expanded stress-related keywords"""
        return [
            # Direct stress (expanded)
            'stress', 'stressed', 'stres', 'tertekan', 'pressure', 'tekanan',
            'overwhelm', 'overwhelmed', 'kewalahan', 'anxiety', 'anxious', 
            'cemas', 'khawatir', 'panic', 'panik', 'worried', 'worry',
            # Implicit stress indicators
            'difficult', 'sulit', 'hard', 'susah', 'challenging', 'menantang',
            'struggle', 'berjuang', 'tough', 'berat'
        ]
    
    def _define_workload_keywords(self) -> List[str]:
        """Define workload keywords with cultural balance"""
        return [
            # Indonesian work terms
            'tugas', 'kerja', 'pekerjaan', 'mengerjakan', 'menyelesaikan',
            'belajar', 'mempelajari', 'membuat', 'mencoba', 'melakukan',
            # English work terms  
            'assignment', 'work', 'job', 'project', 'task', 'study',
            'learning', 'making', 'doing', 'working', 'creating',
            # Academic terms
            'skripsi', 'thesis', 'ujian', 'exam', 'test', 'quiz',
            'research', 'riset', 'analisis', 'analysis',
            # Meeting/presentation
            'meeting', 'rapat', 'presentasi', 'presentation', 'laporan', 'report'
        ]
    
    def _define_negative_emotion_keywords(self) -> List[str]:
        """Define negative emotion keywords"""
        return [
            'frustrated', 'frustasi', 'angry', 'marah', 'upset', 'kesal',
            'sad', 'sedih', 'down', 'disappointed', 'kecewa', 'bored', 'bosan',
            'bad', 'buruk', 'failed', 'gagal', 'wrong', 'salah', 'mistake',
            'lonely', 'kesepian', 'empty', 'kosong', 'meaningless'
        ]
    
    def _define_recovery_keywords(self) -> List[str]:
        """Define recovery keywords with cultural expansion"""
        return [
            # Rest and relaxation
            'rest', 'istirahat', 'relax', 'santai', 'rileks', 'break', 'rehat',
            'vacation', 'liburan', 'holiday', 'cuti', 'weekend', 'libur',
            # Positive activities
            'fun', 'enjoy', 'senang', 'happy', 'bahagia', 'gembira',
            'good', 'bagus', 'great', 'hebat', 'excellent', 'amazing',
            # Social and wellness
            'friends', 'teman', 'family', 'keluarga', 'together', 'bersama',
            'meditation', 'meditasi', 'peaceful', 'damai', 'calm', 'tenang',
            'refresh', 'recharge', 'recovery', 'healing', 'pemulihan'
        ]
    
    def _define_time_pressure_keywords(self) -> List[str]:
        """Define time pressure keywords"""
        return [
            'urgent', 'mendesak', 'rush', 'terburu', 'hurry', 'buru-buru',
            'quick', 'cepat', 'fast', 'immediately', 'segera', 'deadline',
            'late', 'terlambat', 'behind', 'tertinggal', 'again', 'lagi',
            'repeat', 'ulang', 'continue', 'lanjut', 'more', 'lebih'
        ]
    
    def _define_exhaustion_keywords(self) -> List[str]:
        """Define exhaustion keywords"""
        return [
            'tired', 'capek', 'lelah', 'exhausted', 'kelelahan', 'fatigue',
            'fatigue', 'drained', 'terkuras', 'sleepy', 'mengantuk',
            'weak', 'lemah', 'sick', 'sakit', 'headache', 'pusing'
        ]
    
    def detect_language_pattern(self, combined_title: str) -> str:
        """Detect dominant language pattern in title"""
        if pd.isna(combined_title) or combined_title == '':
            return 'unknown'
        
        title_lower = str(combined_title).lower()
        
        indonesian_indicators = ['belajar', 'tugas', 'kerja', 'membuat', 'mengerjakan', 
                               'mempelajari', 'menyelesaikan', 'melakukan', 'mencoba']
        english_indicators = ['morning', 'afternoon', 'evening', 'run', 'walk', 
                            'work', 'study', 'learning', 'making', 'doing']
        
        indonesian_count = sum(1 for word in indonesian_indicators if word in title_lower)
        english_count = sum(1 for word in english_indicators if word in title_lower)
        
        if indonesian_count > english_count:
            return 'indonesian_dominant'
        elif english_count > indonesian_count:
            return 'english_dominant'
        else:
            return 'mixed'
    
    def detect_activity_type(self, row) -> str:
        """Detect dominant activity type"""
        strava_length = row.get('strava_title_length', 0)
        pomokit_length = row.get('pomokit_title_length', 0)
        
        if strava_length > pomokit_length:
            return 'physical_dominant'
        elif pomokit_length > strava_length:
            return 'work_dominant'
        else:
            return 'balanced'
    
    def extract_bias_corrected_features(self, combined_title: str) -> Dict[str, float]:
        """
        Extract bias-corrected features from title text
        
        Args:
            combined_title: Combined title string from activities
            
        Returns:
            Dictionary with bias-corrected features
        """
        if pd.isna(combined_title) or combined_title == '':
            return {
                'stress_count': 0, 'workload_count': 0, 'negative_emotion_count': 0,
                'recovery_count': 0, 'time_pressure_count': 0, 'exhaustion_count': 0,
                'total_words': 0, 'unique_words': 0, 'title_length': 0,
                'activity_count': 0, 'linguistic_complexity': 0, 'emotional_intensity': 0
            }
        
        title_lower = str(combined_title).lower()
        words = title_lower.split()
        
        # Enhanced keyword counts with cultural weighting
        language_pattern = self.detect_language_pattern(combined_title)
        
        # Base keyword counts
        stress_count = sum(1 for keyword in self.stress_keywords if keyword in title_lower)
        workload_count = sum(1 for keyword in self.workload_keywords if keyword in title_lower)
        negative_count = sum(1 for keyword in self.negative_emotion_keywords if keyword in title_lower)
        recovery_count = sum(1 for keyword in self.recovery_keywords if keyword in title_lower)
        time_pressure_count = sum(1 for keyword in self.time_pressure_keywords if keyword in title_lower)
        exhaustion_count = sum(1 for keyword in self.exhaustion_keywords if keyword in title_lower)
        
        # Cultural adaptation weights
        if language_pattern == 'indonesian_dominant':
            # Indonesian users express work more, stress less directly
            workload_count *= 1.1
            stress_count *= 1.3  # Boost rare stress expressions
            negative_count *= 1.3  # Boost rare negative expressions
        elif language_pattern == 'english_dominant':
            # English users may express emotions more directly
            stress_count *= 1.0
            negative_count *= 1.0
            recovery_count *= 1.1  # Boost recovery expressions
        
        # Enhanced linguistic features
        total_words = len(words)
        unique_words = len(set(words))
        title_length = len(combined_title)
        activity_count = len(combined_title.split(' | '))
        
        # Linguistic complexity score
        linguistic_complexity = 0
        if total_words > 0:
            linguistic_complexity = (
                (unique_words / total_words) * 10 +  # Vocabulary diversity
                (total_words / activity_count) * 2 +  # Average words per activity
                (title_length / 100) * 3  # Title elaboration
            )
        
        # Emotional intensity indicators
        exclamation_count = combined_title.count('!')
        question_count = combined_title.count('?')
        caps_ratio = sum(1 for c in combined_title if c.isupper()) / len(combined_title) if combined_title else 0
        
        emotional_intensity = exclamation_count * 2 + question_count * 1.5 + caps_ratio * 5
        
        return {
            'stress_count': stress_count,
            'workload_count': workload_count,
            'negative_emotion_count': negative_count,
            'recovery_count': recovery_count,
            'time_pressure_count': time_pressure_count,
            'exhaustion_count': exhaustion_count,
            'total_words': total_words,
            'unique_words': unique_words,
            'title_length': title_length,
            'activity_count': activity_count,
            'linguistic_complexity': linguistic_complexity,
            'emotional_intensity': emotional_intensity
        }
    
    def calculate_bias_corrected_score(self, features: Dict[str, float],
                                     language_pattern: str, activity_type: str) -> float:
        """
        Calculate bias-corrected fatigue risk score (BALANCED VERSION)

        Args:
            features: Dictionary of title features
            language_pattern: Detected language pattern
            activity_type: Detected activity type

        Returns:
            Bias-corrected fatigue risk score (0-100)
        """
        # Base scoring with REDUCED weights to prevent over-classification
        base_score = (
            # Psychological indicators (reduced weights)
            features['stress_count'] * 8 +
            features['negative_emotion_count'] * 10 +
            features['exhaustion_count'] * 12 +

            # Workload indicators (reduced weights)
            features['workload_count'] * 3 +
            features['time_pressure_count'] * 4 +

            # Activity pattern indicators (reduced)
            max(0, features['activity_count'] - 3) * 2 +  # Only penalize if >3 activities
            features['linguistic_complexity'] * 0.5 +
            features['emotional_intensity'] * 1 +

            # Recovery factors (stronger protective effect)
            - features['recovery_count'] * 8
        )

        # Cultural adaptation adjustments (REDUCED)
        if language_pattern == 'indonesian_dominant':
            # Adjust for cultural expression patterns
            if features['workload_count'] >= 3 and features['recovery_count'] == 0:
                base_score += 5  # Reduced penalty
        elif language_pattern == 'english_dominant':
            # English users may express stress more directly
            if features['stress_count'] == 0 and features['workload_count'] >= 3:
                base_score += 3  # Reduced penalty

        # Activity type adjustments (REDUCED)
        if activity_type == 'physical_dominant':
            # Physical activity users may have hidden fatigue
            if features['workload_count'] >= 2:
                base_score += 3  # Reduced boost
        elif activity_type == 'work_dominant':
            # Work-focused users
            if features['recovery_count'] == 0 and features['workload_count'] >= 2:
                base_score += 5  # Reduced penalty

        # Zero keyword adjustment (REDUCED)
        total_keywords = (features['stress_count'] + features['workload_count'] +
                         features['negative_emotion_count'] + features['recovery_count'] +
                         features['time_pressure_count'] + features['exhaustion_count'])

        if total_keywords == 0:
            # Use linguistic complexity for zero-keyword cases
            if features['linguistic_complexity'] > 20:
                base_score += 8  # Reduced boost
            elif features['activity_count'] >= 5:
                base_score += 5  # Reduced boost

        # Normalize to 0-100 scale
        return max(0, min(100, base_score))
    
    def classify_bias_corrected_risk(self, score: float, features: Dict[str, float],
                                   language_pattern: str, activity_type: str) -> str:
        """
        Classify fatigue risk using BALANCED bias-corrected approach

        Args:
            score: Bias-corrected fatigue score
            features: Title features dictionary
            language_pattern: Language pattern
            activity_type: Activity type

        Returns:
            Risk classification: 'low_risk', 'medium_risk', 'high_risk'
        """
        # BALANCED thresholds (no overlap, more conservative)
        HIGH_THRESHOLD = 35  # Reduced from 50
        MEDIUM_THRESHOLD = 15  # Reduced from 25

        # Multi-pathway classification (MORE RESTRICTIVE)

        # High risk pathways (MORE STRICT)
        high_risk_conditions = [
            score >= HIGH_THRESHOLD,
            features['stress_count'] >= 2,
            features['exhaustion_count'] >= 2,
            features['negative_emotion_count'] >= 2,
            (features['workload_count'] >= 5) and (features['recovery_count'] == 0),  # Increased threshold
            features['time_pressure_count'] >= 4,  # Increased threshold
            # Cultural-specific pathways (MORE STRICT)
            (language_pattern == 'indonesian_dominant') and
            (features['workload_count'] >= 4) and (features['recovery_count'] == 0),  # Increased
            # Activity-specific pathways (MORE STRICT)
            (activity_type == 'physical_dominant') and
            (features['workload_count'] >= 3) and (features['stress_count'] >= 1)  # Increased
        ]

        if any(high_risk_conditions):
            return 'high_risk'

        # Medium risk pathways (BALANCED)
        medium_risk_conditions = [
            score >= MEDIUM_THRESHOLD,
            features['stress_count'] >= 1,
            features['exhaustion_count'] >= 1,
            features['negative_emotion_count'] >= 1,
            features['workload_count'] >= 4,  # Increased threshold
            features['time_pressure_count'] >= 3,  # Increased threshold
            (features['workload_count'] >= 3) and (features['recovery_count'] == 0),  # Increased
            features['activity_count'] >= 6,  # Increased threshold for overactivity
            # Zero keyword but high complexity (MORE STRICT)
            (features['stress_count'] + features['workload_count'] +
             features['negative_emotion_count'] == 0) and
            (features['linguistic_complexity'] > 25)  # Increased threshold
        ]

        if any(medium_risk_conditions):
            return 'medium_risk'

        # Low risk (default)
        return 'low_risk'
    
    def process_bias_corrected_classification(self, data_path: str) -> pd.DataFrame:
        """
        Complete bias-corrected classification pipeline
        
        Args:
            data_path: Path to the processed dataset
            
        Returns:
            DataFrame with bias-corrected classification
        """
        logger.info("Starting bias-corrected fatigue risk classification...")
        
        # Load data
        df = pd.read_csv(data_path)
        logger.info(f"Loaded dataset with {len(df)} observations")
        
        # Extract bias-corrected features
        logger.info("Extracting bias-corrected title features...")
        corrected_features_list = df['combined_titles'].apply(self.extract_bias_corrected_features)
        
        # Detect patterns
        df['language_pattern'] = df['combined_titles'].apply(self.detect_language_pattern)
        df['activity_type'] = df.apply(self.detect_activity_type, axis=1)
        
        # Convert features to columns
        feature_names = ['stress_count', 'workload_count', 'negative_emotion_count',
                        'recovery_count', 'time_pressure_count', 'exhaustion_count',
                        'total_words', 'unique_words', 'title_length', 'activity_count',
                        'linguistic_complexity', 'emotional_intensity']
        
        for feature in feature_names:
            df[f'corrected_{feature}'] = [features[feature] for features in corrected_features_list]
        
        # Calculate bias-corrected scores
        logger.info("Calculating bias-corrected fatigue scores...")
        df['corrected_fatigue_score'] = [
            self.calculate_bias_corrected_score(features, lang, activity)
            for features, lang, activity in zip(
                corrected_features_list, df['language_pattern'], df['activity_type']
            )
        ]
        
        # Classify with bias correction
        logger.info("Classifying with bias correction...")
        df['corrected_fatigue_risk'] = [
            self.classify_bias_corrected_risk(score, features, lang, activity)
            for score, features, lang, activity in zip(
                df['corrected_fatigue_score'], corrected_features_list,
                df['language_pattern'], df['activity_type']
            )
        ]
        
        logger.info("Bias-corrected classification completed successfully")

        # Save results to CSV
        output_path = "dataset/processed/fatigue_classified.csv"
        df.to_csv(output_path, index=False)
        logger.info(f"Results saved to: {output_path}")

        return df

    def visualize_classification_results(self, data: pd.DataFrame, save_plots: bool = True) -> None:
        """
        Create comprehensive visualizations for bias-corrected classification results

        Args:
            data: DataFrame with classification results
            save_plots: Whether to save plots to files
        """
        logger.info("Creating classification result visualizations...")

        # Create figure with subplots
        fig, axes = plt.subplots(2, 3, figsize=(18, 12))
        fig.suptitle('Bias-Corrected Fatigue Risk Classification Analysis', fontsize=16, fontweight='bold')

        # 1. Risk distribution pie chart
        if 'corrected_fatigue_risk' in data.columns:
            risk_counts = data['corrected_fatigue_risk'].value_counts()
            colors = ['green', 'orange', 'red']
            axes[0, 0].pie(risk_counts.values, labels=risk_counts.index, autopct='%1.1f%%',
                          colors=colors, startangle=90)
            axes[0, 0].set_title('Fatigue Risk Distribution')

        # 2. Score distribution by risk level
        if 'corrected_fatigue_score' in data.columns and 'corrected_fatigue_risk' in data.columns:
            for risk_level in data['corrected_fatigue_risk'].unique():
                subset = data[data['corrected_fatigue_risk'] == risk_level]['corrected_fatigue_score']
                axes[0, 1].hist(subset, alpha=0.7, label=risk_level, bins=15)
            axes[0, 1].set_title('Score Distribution by Risk Level')
            axes[0, 1].set_xlabel('Corrected Fatigue Score')
            axes[0, 1].set_ylabel('Frequency')
            axes[0, 1].legend()
            axes[0, 1].grid(True, alpha=0.3)

        # 3. Language pattern vs risk level
        if 'language_pattern' in data.columns and 'corrected_fatigue_risk' in data.columns:
            crosstab = pd.crosstab(data['language_pattern'], data['corrected_fatigue_risk'])
            crosstab.plot(kind='bar', ax=axes[0, 2], stacked=True)
            axes[0, 2].set_title('Risk Level by Language Pattern')
            axes[0, 2].set_xlabel('Language Pattern')
            axes[0, 2].set_ylabel('Count')
            axes[0, 2].tick_params(axis='x', rotation=45)
            axes[0, 2].legend(title='Risk Level')

        # 4. Activity type vs risk level
        if 'activity_type' in data.columns and 'corrected_fatigue_risk' in data.columns:
            crosstab = pd.crosstab(data['activity_type'], data['corrected_fatigue_risk'])
            crosstab.plot(kind='bar', ax=axes[1, 0], stacked=True)
            axes[1, 0].set_title('Risk Level by Activity Type')
            axes[1, 0].set_xlabel('Activity Type')
            axes[1, 0].set_ylabel('Count')
            axes[1, 0].tick_params(axis='x', rotation=45)
            axes[1, 0].legend(title='Risk Level')

        # 5. Feature importance heatmap
        feature_cols = [col for col in data.columns if col.startswith('corrected_') and col.endswith('_count')]
        if feature_cols:
            feature_data = data[feature_cols + ['corrected_fatigue_risk']]
            # Calculate mean feature values by risk level
            feature_means = feature_data.groupby('corrected_fatigue_risk')[feature_cols].mean()

            sns.heatmap(feature_means.T, annot=True, cmap='YlOrRd', ax=axes[1, 1], fmt='.2f')
            axes[1, 1].set_title('Feature Intensity by Risk Level')
            axes[1, 1].set_xlabel('Risk Level')
            axes[1, 1].set_ylabel('Features')

        # 6. Score vs key features scatter
        if 'corrected_fatigue_score' in data.columns and 'corrected_stress_count' in data.columns:
            scatter = axes[1, 2].scatter(data['corrected_stress_count'], data['corrected_fatigue_score'],
                                       c=data['corrected_fatigue_risk'].map({'low_risk': 0, 'medium_risk': 1, 'high_risk': 2}),
                                       cmap='RdYlGn_r', alpha=0.6)
            axes[1, 2].set_title('Fatigue Score vs Stress Count')
            axes[1, 2].set_xlabel('Stress Count')
            axes[1, 2].set_ylabel('Fatigue Score')
            axes[1, 2].grid(True, alpha=0.3)
            plt.colorbar(scatter, ax=axes[1, 2], label='Risk Level')

        plt.tight_layout()

        if save_plots:
            # Create results/visualizations directory
            viz_dir = Path("results/visualizations")
            viz_dir.mkdir(parents=True, exist_ok=True)
            plot_path = viz_dir / 'classification_analysis.png'
            plt.savefig(plot_path, dpi=300, bbox_inches='tight')
            logger.info(f"Saved classification analysis plot to {plot_path}")

        plt.close()

    def visualize_feature_analysis(self, data: pd.DataFrame, save_plots: bool = True) -> None:
        """
        Create detailed feature analysis visualizations

        Args:
            data: DataFrame with classification results
            save_plots: Whether to save plots to files
        """
        logger.info("Creating feature analysis visualizations...")

        # Create figure with subplots
        fig, axes = plt.subplots(2, 2, figsize=(15, 10))
        fig.suptitle('Feature Analysis for Bias-Corrected Classification', fontsize=16, fontweight='bold')

        # 1. Keyword frequency analysis
        keyword_cols = [col for col in data.columns if col.startswith('corrected_') and col.endswith('_count')]
        if keyword_cols:
            keyword_means = data[keyword_cols].mean().sort_values(ascending=True)
            keyword_means.plot(kind='barh', ax=axes[0, 0])
            axes[0, 0].set_title('Average Keyword Frequency')
            axes[0, 0].set_xlabel('Average Count')
            axes[0, 0].grid(True, alpha=0.3)

        # 2. Linguistic complexity distribution
        if 'corrected_linguistic_complexity' in data.columns:
            axes[0, 1].hist(data['corrected_linguistic_complexity'], bins=20, alpha=0.7, color='skyblue', edgecolor='black')
            axes[0, 1].set_title('Linguistic Complexity Distribution')
            axes[0, 1].set_xlabel('Linguistic Complexity Score')
            axes[0, 1].set_ylabel('Frequency')
            axes[0, 1].grid(True, alpha=0.3)

        # 3. Emotional intensity vs risk level
        if 'corrected_emotional_intensity' in data.columns and 'corrected_fatigue_risk' in data.columns:
            risk_levels = data['corrected_fatigue_risk'].unique()
            for risk in risk_levels:
                subset = data[data['corrected_fatigue_risk'] == risk]['corrected_emotional_intensity']
                axes[1, 0].hist(subset, alpha=0.6, label=risk, bins=15)
            axes[1, 0].set_title('Emotional Intensity by Risk Level')
            axes[1, 0].set_xlabel('Emotional Intensity')
            axes[1, 0].set_ylabel('Frequency')
            axes[1, 0].legend()
            axes[1, 0].grid(True, alpha=0.3)

        # 4. Recovery vs workload balance
        if 'corrected_recovery_count' in data.columns and 'corrected_workload_count' in data.columns:
            scatter = axes[1, 1].scatter(data['corrected_workload_count'], data['corrected_recovery_count'],
                                       c=data['corrected_fatigue_risk'].map({'low_risk': 0, 'medium_risk': 1, 'high_risk': 2}),
                                       cmap='RdYlGn_r', alpha=0.6)
            axes[1, 1].set_title('Recovery vs Workload Balance')
            axes[1, 1].set_xlabel('Workload Count')
            axes[1, 1].set_ylabel('Recovery Count')
            axes[1, 1].grid(True, alpha=0.3)
            plt.colorbar(scatter, ax=axes[1, 1], label='Risk Level')

        plt.tight_layout()

        if save_plots:
            # Create results/visualizations directory
            viz_dir = Path("results/visualizations")
            viz_dir.mkdir(parents=True, exist_ok=True)
            plot_path = viz_dir / 'feature_analysis.png'
            plt.savefig(plot_path, dpi=300, bbox_inches='tight')
            logger.info(f"Saved feature analysis plot to {plot_path}")

        plt.close()

    def generate_classification_report(self, data: pd.DataFrame) -> None:
        """
        Generate comprehensive text report for classification results

        Args:
            data: DataFrame with classification results
        """
        logger.info("Generating classification report...")

        print("\n" + "="*70)
        print("BIAS-CORRECTED FATIGUE RISK CLASSIFICATION REPORT")
        print("="*70)
        print(f"Report generated on: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"Dataset size: {data.shape[0]} observations")

        # Risk distribution analysis
        print("\n" + "-"*50)
        print("RISK DISTRIBUTION ANALYSIS")
        print("-"*50)

        risk_dist = data['corrected_fatigue_risk'].value_counts()
        risk_pct = (risk_dist / len(data) * 100).round(1)

        for risk_level in ['low_risk', 'medium_risk', 'high_risk']:
            if risk_level in risk_dist.index:
                count = risk_dist[risk_level]
                pct = risk_pct[risk_level]
                print(f"{risk_level.replace('_', ' ').title()}: {count} ({pct}%)")

        # Score statistics
        print("\n" + "-"*50)
        print("FATIGUE SCORE STATISTICS")
        print("-"*50)

        score_stats = data['corrected_fatigue_score'].describe()
        print(f"Mean Score: {score_stats['mean']:.2f}")
        print(f"Median Score: {score_stats['50%']:.2f}")
        print(f"Standard Deviation: {score_stats['std']:.2f}")
        print(f"Score Range: {score_stats['min']:.2f} - {score_stats['max']:.2f}")

        # Language pattern analysis
        if 'language_pattern' in data.columns:
            print("\n" + "-"*50)
            print("LANGUAGE PATTERN ANALYSIS")
            print("-"*50)

            lang_risk = pd.crosstab(data['language_pattern'], data['corrected_fatigue_risk'], normalize='index') * 100
            print(lang_risk.round(1))

        # Activity type analysis
        if 'activity_type' in data.columns:
            print("\n" + "-"*50)
            print("ACTIVITY TYPE ANALYSIS")
            print("-"*50)

            activity_risk = pd.crosstab(data['activity_type'], data['corrected_fatigue_risk'], normalize='index') * 100
            print(activity_risk.round(1))

        # Feature importance analysis
        feature_cols = [col for col in data.columns if col.startswith('corrected_') and col.endswith('_count')]
        if feature_cols:
            print("\n" + "-"*50)
            print("FEATURE IMPORTANCE ANALYSIS")
            print("-"*50)

            high_risk_data = data[data['corrected_fatigue_risk'] == 'high_risk']
            if len(high_risk_data) > 0:
                print("Average feature values for HIGH RISK cases:")
                for feature in feature_cols:
                    avg_val = high_risk_data[feature].mean()
                    print(f"  {feature.replace('corrected_', '').replace('_', ' ').title()}: {avg_val:.2f}")

        print("\n" + "="*70)
        print("CLASSIFICATION REPORT COMPLETED")
        print("="*70)

    def create_comprehensive_classification_report(self, data: pd.DataFrame, save_plots: bool = True) -> None:
        """
        Create comprehensive classification analysis with all visualizations and reports

        Args:
            data: DataFrame with classification results
            save_plots: Whether to save plots to files
        """
        logger.info("Creating comprehensive classification report...")

        # Generate all visualizations
        self.visualize_classification_results(data, save_plots)
        self.visualize_feature_analysis(data, save_plots)

        # Generate text report
        self.generate_classification_report(data)

        logger.info("Comprehensive classification report completed!")


def main():
    """Main function for bias-corrected classification"""
    # Initialize classifier
    classifier = BiasCorrectedTitleClassifier()
    
    # Process classification
    data_path = "dataset/processed/weekly_merged_dataset_with_gamification.csv"
    
    if not Path(data_path).exists():
        print(f"❌ Dataset not found: {data_path}")
        print("Please run the main pipeline first to generate the processed dataset.")
        return
    
    # Run bias-corrected classification
    classified_data = classifier.process_bias_corrected_classification(data_path)

    output_path = "dataset/processed/fatigue_classified.csv"
    print("🎉 Bias-Corrected Classification Completed!")
    print(f"📊 Results saved to: {output_path}")
    print(f"📈 Dataset shape: {classified_data.shape}")

    # Quick analysis
    corrected_dist = classified_data['corrected_fatigue_risk'].value_counts()
    print(f"🔍 Corrected distribution: {(corrected_dist / len(classified_data) * 100).round(1).to_dict()}")

    # Create comprehensive visualizations and report
    print("\n📊 Generating comprehensive classification analysis...")
    classifier.create_comprehensive_classification_report(classified_data, save_plots=True)

    print("\n✅ Analysis completed! Check 'results/visualizations/' for visualization files:")
    print("  - classification_analysis.png")
    print("  - feature_analysis.png")

    return classified_data


if __name__ == "__main__":
    main()
