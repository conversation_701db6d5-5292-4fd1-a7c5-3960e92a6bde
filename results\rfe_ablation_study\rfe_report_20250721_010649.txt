================================================================================
🔍 RFE ABLATION STUDY REPORT
================================================================================

📋 DATASET INFORMATION:
   • Dataset Path: dataset\processed\safe_ml_fatigue_dataset.csv
   • Target Column: fatigue_risk
   • Total Features: 20
   • Total Samples: 291

🤖 ALGORITHMS TESTED:
   1. Logistic Regression (logistic_regression)
   2. Random Forest (random_forest)
   3. Gradient Boosting (gradient_boosting)
   4. Support Vector Machine (svm)
   5. XGBoost (xgboost)

📊 FEATURE COUNT RANGE TESTED:
   • Range: [5, 10, 15, 20, 25, 30]
   • Total Experiments: 20

🏆 BEST OVERALL PERFORMANCE:
   • Algorithm: XGBoost
   • Features Used: 5
   • Accuracy: 0.9554 ± 0.0318
   • F1-Score: 0.9464
   • ✅ Overfitting Risk: 0.0051 (LOW - Train-Test gap < 5%)

🎯 BEST PERFORMANCE BY ALGORITHM:
   • Logistic Regression:
     - Features: 15
     - Accuracy: 0.6224 ± 0.0821
     - F1-Score: 0.6250
     - ⚠️ Overfitting: 0.1001 (HIGH)
   • Random Forest:
     - Features: 10
     - Accuracy: 0.9520 ± 0.0367
     - F1-Score: 0.9400
     - ✅ Overfitting: 0.0446 (LOW)
   • Gradient Boosting:
     - Features: 10
     - Accuracy: 0.9244 ± 0.0444
     - F1-Score: 0.8971
     - ⚠️ Overfitting: 0.0756 (MODERATE)
   • Support Vector Machine:
     - Features: 15
     - Accuracy: 0.6565 ± 0.0392
     - F1-Score: 0.6562
     - ✅ Overfitting: 0.0497 (LOW)
   • XGBoost:
     - Features: 5
     - Accuracy: 0.9554 ± 0.0318
     - F1-Score: 0.9464
     - ✅ Overfitting: 0.0051 (LOW)

⭐ MOST FREQUENTLY SELECTED FEATURES:
    1. consistency_score: 20 times (100.0%)
    2. pomokit_title_count: 19 times (95.0%)
    3. total_cycles: 19 times (95.0%)
    4. productivity_points: 18 times (90.0%)
    5. work_days: 17 times (85.0%)
    6. strava_title_length: 15 times (75.0%)
    7. title_balance_ratio: 14 times (70.0%)
    8. pomokit_title_length: 14 times (70.0%)
    9. strava_title_count: 13 times (65.0%)
   10. gamification_balance: 12 times (60.0%)

🎖️ MOST CONSISTENT FEATURES (in top 10 results):
    1. pomokit_title_count: 10/10 top results
    2. productivity_points: 10/10 top results
    3. consistency_score: 10/10 top results
    4. total_cycles: 9/10 top results
    5. strava_title_length: 8/10 top results
    6. strava_title_count: 8/10 top results
    7. activity_days: 7/10 top results
    8. work_days: 7/10 top results
    9. total_distance_km: 7/10 top results
   10. total_title_diversity: 6/10 top results

🔍 OVERFITTING ANALYSIS:
   • Best Model Overfitting Score: 0.0051
   • ✅ LOW OVERFITTING RISK: Good generalization expected

   📊 Overfitting by Algorithm:
     ⚠️ Logistic Regression: 0.1001 (HIGH)
     ✅ Random Forest: 0.0446 (LOW)
     ⚠️ Gradient Boosting: 0.0756 (MODERATE)
     ✅ Support Vector Machine: 0.0497 (LOW)
     ✅ XGBoost: 0.0051 (LOW)

📊 COMPREHENSIVE FEATURE IMPORTANCE ANALYSIS:
   • Algorithm: XGBoost
   • Method: Permutation Importance + Ablation Analysis
   • Total Features: 5
   • Baseline Accuracy: 0.9520

   🎯 Feature Impact Analysis (Top 10):
      1. ⚪ activity_days:
         - Baseline: 0.9520 | Without: 0.9588
         - Impact: -0.0068 (-0.72%)
         - MINIMAL - Negligible performance impact
      2. ⚪ pomokit_title_count:
         - Baseline: 0.9520 | Without: 0.9520
         - Impact: 0.0000 (0.00%)
         - MINIMAL - Negligible performance impact
      3. ⚪ productivity_points:
         - Baseline: 0.9520 | Without: 0.9520
         - Impact: 0.0000 (0.00%)
         - MINIMAL - Negligible performance impact
      4. ⚪ total_cycles:
         - Baseline: 0.9520 | Without: 0.9520
         - Impact: 0.0000 (0.00%)
         - MINIMAL - Negligible performance impact
      5. ⚪ consistency_score:
         - Baseline: 0.9520 | Without: 0.9520
         - Impact: 0.0000 (0.00%)
         - MINIMAL - Negligible performance impact

   📊 Permutation Importance Summary:
     • activity_days: 2.81% (score: 0.0175 ±0.0082)
     • pomokit_title_count: 33.74% (score: 0.2107 ±0.0169)
     • productivity_points: 0.50% (score: 0.0031 ±0.0019)
     • total_cycles: 0.00% (score: 0.0000 ±0.0000)
     • consistency_score: 62.96% (score: 0.3931 ±0.0252)

✅ RECOMMENDED OPTIMAL FEATURE SET:
   • Algorithm: XGBoost
   • Number of Features: 5
   • Selected Features:
      1. activity_days
      2. consistency_score
      3. pomokit_title_count
      4. productivity_points
      5. total_cycles
================================================================================