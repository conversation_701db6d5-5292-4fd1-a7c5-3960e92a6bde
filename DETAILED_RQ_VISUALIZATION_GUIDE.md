# 📊 Panduan Detail Visualisasi untuk Setiap Research Question

## 🎯 **MAPPING VISUALISASI BERDASARKAN RESEARCH QUESTIONS**

---

## **1️⃣ RQ1: Faktor Signifikan & Ablation Study**
### **"Faktor-faktor apa saja yang paling signifikan dalam memprediksi risiko fatigue pada mahasiswa, dan bagaimana kontribusi individual setiap fitur dapat divalidasi melalui ablation study"**

### **📊 VISUALISASI UTAMA:**

#### **A. `feature_analysis.png`** ⭐⭐⭐⭐⭐
- **Lokasi**: `results/visualizations/feature_analysis.png`
- **Sumber**: `bias_corrected_title_classifier.py`
- **Konten Spesifik**:
  - Feature importance ranking (top 10-15 features)
  - Contribution scores untuk setiap fitur
  - Comparison antara different feature types
- **Menjawab**: Faktor mana yang paling signifikan
- **Untuk Laporan**: Section 4.1.1 - Feature Importance Analysis

#### **B. `rfe_analysis_[timestamp].png`** ⭐⭐⭐⭐⭐
- **Lokasi**: `results/visualizations/rfe_analysis_*.png`
- **Sumber**: `rfe_ablation_study.py`
- **Konten Spesifik**:
  - Ablation study results dengan different algorithms
  - Performance curves dengan varying feature counts
  - Optimal feature selection validation
- **Menjawab**: Validasi kontribusi individual fitur
- **Untuk Laporan**: Section 4.1.2 - Ablation Study Validation

#### **C. `feature_selection_process_[timestamp].png`** ⭐⭐⭐⭐
- **Lokasi**: `results/visualizations/feature_selection_process_*.png`
- **Sumber**: `rfe_ablation_study.py`
- **Konten Spesifik**:
  - Step-by-step feature elimination process
  - Performance impact of removing each feature
  - Cross-validation results
- **Menjawab**: Proses validasi systematic
- **Untuk Laporan**: Section 4.1.3 - Feature Selection Process

---

## **2️⃣ RQ2: Efektivitas Model ML**
### **"Bagaimana efektivitas model machine learning dalam mengklasifikasikan tingkat risiko fatigue berdasarkan data aktivitas kardiovaskular dan produktivitas"**

### **📊 VISUALISASI UTAMA:**

#### **A. `classification_analysis.png`** ⭐⭐⭐⭐⭐
- **Lokasi**: `results/visualizations/classification_analysis.png`
- **Sumber**: `bias_corrected_title_classifier.py`
- **Konten Spesifik**:
  - Confusion matrix untuk fatigue risk classification
  - Accuracy, precision, recall metrics
  - Risk distribution (high/medium/low risk)
- **Menjawab**: Seberapa efektif model dalam klasifikasi
- **Untuk Laporan**: Section 4.2.1 - Classification Performance

#### **B. `data_overview.png`** ⭐⭐⭐⭐
- **Lokasi**: `results/visualizations/data_overview.png`
- **Sumber**: `data_processor.py`
- **Konten Spesifik**:
  - Scatter plot: Physical Activity vs Productivity
  - Distribution plots untuk key metrics
  - Relationship visualization
- **Menjawab**: Hubungan dasar antara kardiovaskular dan produktivitas
- **Untuk Laporan**: Section 4.2.2 - Data Relationships

#### **C. `regression_analysis.png`** ⭐⭐⭐⭐
- **Lokasi**: `results/visualizations/regression_analysis.png`
- **Sumber**: `create_research_specific_visualizations.py`
- **Konten Spesifik**:
  - Regression lines dengan R² dan p-values
  - Statistical significance testing
  - Effect size analysis
- **Menjawab**: Statistical validation of relationships
- **Untuk Laporan**: Section 4.2.3 - Statistical Validation

---

## **3️⃣ RQ3: Title-Only Analysis**
### **"Apakah analisis berbasis judul aktivitas (title-only analysis) dapat memberikan prediksi fatigue yang akurat tanpa memerlukan data kuantitatif lengkap"**

### **📊 VISUALISASI UTAMA:**

#### **A. `feature_analysis.png`** ⭐⭐⭐⭐⭐
- **Fokus Khusus**: Title-based vs Quantitative features
- **Konten untuk RQ3**:
  - Comparison of linguistic features vs numerical features
  - Title-based feature importance ranking
  - Keyword frequency analysis
- **Menjawab**: Kontribusi title features vs data kuantitatif
- **Untuk Laporan**: Section 4.3.1 - Title Feature Analysis

#### **B. `classification_analysis.png`** ⭐⭐⭐⭐
- **Fokus Khusus**: Title-only model performance
- **Konten untuk RQ3**:
  - Accuracy comparison: title-only vs full data
  - Performance metrics untuk different input types
  - Language pattern analysis
- **Menjawab**: Akurasi prediksi title-only
- **Untuk Laporan**: Section 4.3.2 - Title-Only Performance

#### **C. `bias_correction_impact.png`** ⭐⭐⭐
- **Lokasi**: `results/visualizations/bias_correction_impact.png`
- **Sumber**: `feature_filter2.py`
- **Konten Spesifik**:
  - Before/after bias correction comparison
  - Language pattern detection (Indonesian/English)
  - Cultural bias adjustment effects
- **Menjawab**: Impact of linguistic bias correction
- **Untuk Laporan**: Section 4.3.3 - Bias Correction Analysis

---

## **4️⃣ RQ4: Gamifikasi & Achievement Rate**
### **"Bagaimana pola gamifikasi dan achievement rate mempengaruhi konsistensi aktivitas fisik dan risiko fatigue mahasiswa"**

### **📊 VISUALISASI UTAMA:**

#### **A. `gamification_analysis.png`** ⭐⭐⭐⭐⭐
- **Lokasi**: `results/visualizations/gamification_analysis.png`
- **Sumber**: `data_processor.py`
- **Konten Spesifik**:
  - Activity points vs Productivity points scatter
  - Achievement rate quartile distribution
  - Gamification balance analysis
  - Weekly efficiency vs achievement rate
- **Menjawab**: Pola gamifikasi dan achievement
- **Untuk Laporan**: Section 4.4.1 - Gamification Patterns

#### **B. `intervention_effectiveness.png`** ⭐⭐⭐⭐⭐
- **Lokasi**: `results/visualizations/intervention_effectiveness.png`
- **Sumber**: `create_research_specific_visualizations.py`
- **Konten Spesifik**:
  - Achievement rate by activity level
  - Gamification balance distribution
  - Effectiveness by user activity level
  - Consistency vs productivity relationship
- **Menjawab**: Efektivitas gamifikasi terhadap konsistensi
- **Untuk Laporan**: Section 4.4.2 - Intervention Effectiveness

#### **C. `time_series_analysis.png`** ⭐⭐⭐⭐
- **Lokasi**: `results/visualizations/time_series_analysis.png`
- **Sumber**: `data_processor.py`
- **Konten Spesifik**:
  - Weekly trends untuk distance, cycles, consistency
  - Achievement rate over time
  - Seasonal patterns analysis
- **Menjawab**: Konsistensi aktivitas dari waktu ke waktu
- **Untuk Laporan**: Section 4.4.3 - Temporal Consistency

---

## 📋 **STRUKTUR LAPORAN YANG DISARANKAN**

### **BAB 4: HASIL PENELITIAN**

#### **4.1 Analisis Faktor Signifikan (RQ1)**
- **4.1.1** Feature Importance Analysis → `feature_analysis.png`
- **4.1.2** Ablation Study Results → `rfe_analysis_*.png`
- **4.1.3** Feature Selection Validation → `feature_selection_process_*.png`

#### **4.2 Efektivitas Model Machine Learning (RQ2)**
- **4.2.1** Classification Performance → `classification_analysis.png`
- **4.2.2** Data Relationship Analysis → `data_overview.png`
- **4.2.3** Statistical Validation → `regression_analysis.png`

#### **4.3 Analisis Title-Only (RQ3)**
- **4.3.1** Title Feature Analysis → `feature_analysis.png`
- **4.3.2** Title-Only Performance → `classification_analysis.png`
- **4.3.3** Bias Correction Impact → `bias_correction_impact.png`

#### **4.4 Analisis Gamifikasi (RQ4)**
- **4.4.1** Gamification Patterns → `gamification_analysis.png`
- **4.4.2** Intervention Effectiveness → `intervention_effectiveness.png`
- **4.4.3** Temporal Consistency → `time_series_analysis.png`

---

## ✅ **RINGKASAN VISUALISASI UNTUK LAPORAN**

### **🏆 WAJIB (8 Visualisasi Utama)**
1. `feature_analysis.png` - RQ1 & RQ3
2. `rfe_analysis_*.png` - RQ1 & RQ2
3. `classification_analysis.png` - RQ2 & RQ3
4. `gamification_analysis.png` - RQ4
5. `intervention_effectiveness.png` - RQ4
6. `data_overview.png` - RQ2
7. `regression_analysis.png` - RQ2
8. `time_series_analysis.png` - RQ4

### **🎯 PENDUKUNG (3 Visualisasi Tambahan)**
9. `feature_selection_process_*.png` - RQ1
10. `bias_correction_impact.png` - RQ3
11. `correlation_matrix.png` - RQ1

**Total: 11 visualisasi yang menjawab semua 4 research questions secara komprehensif!**
