================================================================================
🔍 SHAP ABLATION STUDY REPORT
================================================================================

📋 DATASET INFORMATION:
   • Dataset Path: dataset/processed/safe_ml_fatigue_dataset.csv
   • Target Column: fatigue_risk
   • Total Features: 20
   • Total Samples: 291
   • Target Distribution: {'medium_risk': 145, 'high_risk': 100, 'low_risk': 46}

🤖 MODEL PERFORMANCE:
   • Logistic Regression:
     - Test Accuracy: 0.6441
     - Test F1-Score: 0.6412
     - CV Accuracy: 0.6764 (±0.0526)
     - CV F1-Score: 0.6587 (±0.0560)
   • Random Forest:
     - Test Accuracy: 0.9492
     - Test F1-Score: 0.9520
     - CV Accuracy: 0.9525 (±0.0315)
     - CV F1-Score: 0.9534 (±0.0312)
   • Gradient Boosting:
     - Test Accuracy: 0.9492
     - Test F1-Score: 0.9520
     - CV Accuracy: 0.9568 (±0.0236)
     - CV F1-Score: 0.9574 (±0.0234)
   • XGBoost:
     - Test Accuracy: 0.9322
     - Test F1-Score: 0.9333
     - CV Accuracy: 0.9438 (±0.0353)
     - CV F1-Score: 0.9440 (±0.0350)

🎯 GLOBAL SHAP FEATURE IMPORTANCE:

   📊 Logistic Regression:
      1. 🔥 consistency_score: 0.2603
      2. 🔥 work_days: 0.1233
      3. 🔸 gamification_balance: 0.0243
      4. ▫️ total_distance_km: 0.0175
      5. ▫️ strava_title_count: 0.0145
      6. ▫️ pomokit_title_length: 0.0143
      7. ▫️ title_balance_ratio: 0.0127
      8. ▫️ strava_title_length: 0.0090
      9. ▫️ strava_unique_words: 0.0077
     10. ▫️ total_time_minutes: 0.0065

   📊 Random Forest:
      1. 🔥 consistency_score: 0.2601
      2. 🔥 work_days: 0.1227
      3. 🔸 gamification_balance: 0.0239
      4. ▫️ total_distance_km: 0.0163
      5. ▫️ title_balance_ratio: 0.0148
      6. ▫️ strava_title_count: 0.0140
      7. ▫️ pomokit_title_length: 0.0135
      8. ▫️ strava_title_length: 0.0101
      9. ▫️ total_time_minutes: 0.0084
     10. ▫️ strava_unique_words: 0.0082

   📊 Gradient Boosting:
      1. 🔥 consistency_score: 0.2603
      2. 🔥 work_days: 0.1233
      3. 🔸 gamification_balance: 0.0240
      4. ▫️ total_distance_km: 0.0162
      5. ▫️ title_balance_ratio: 0.0145
      6. ▫️ strava_title_count: 0.0140
      7. ▫️ pomokit_title_length: 0.0133
      8. ▫️ strava_title_length: 0.0105
      9. ▫️ total_time_minutes: 0.0084
     10. ▫️ strava_unique_words: 0.0082

   📊 XGBoost:
      1. 🔥 consistency_score: 0.2600
      2. 🔥 work_days: 0.1225
      3. 🔸 gamification_balance: 0.0237
      4. ▫️ total_distance_km: 0.0166
      5. ▫️ title_balance_ratio: 0.0150
      6. ▫️ strava_title_count: 0.0145
      7. ▫️ pomokit_title_length: 0.0134
      8. ▫️ strava_title_length: 0.0099
      9. ▫️ total_time_minutes: 0.0093
     10. ▫️ strava_unique_words: 0.0088

🔬 SHAP ANALYSIS ADVANTAGES:
   • Theoretically grounded (Shapley values from game theory)
   • Individual prediction explanations available
   • Captures feature interactions
   • Model-agnostic interpretability
   • Positive/negative contribution analysis

🎖️ FEATURE CONSISTENCY ACROSS ALGORITHMS:
   Most consistent features (appearing in multiple top-5 lists):
     • consistency_score: 4/4 algorithms (100.0%)
     • work_days: 4/4 algorithms (100.0%)
     • gamification_balance: 4/4 algorithms (100.0%)
     • total_distance_km: 4/4 algorithms (100.0%)
     • title_balance_ratio: 3/4 algorithms (75.0%)

✅ SHAP-BASED RECOMMENDATIONS:
   • Best performing algorithm: Random Forest (0.9492 accuracy)
   • Top 5 SHAP features for production:
     1. consistency_score (SHAP: 0.2601)
     2. work_days (SHAP: 0.1227)
     3. gamification_balance (SHAP: 0.0239)
     4. total_distance_km (SHAP: 0.0163)
     5. title_balance_ratio (SHAP: 0.0148)

📊 SHAP ANALYSIS SUMMARY:
   • Total algorithms analyzed: 4
   • Total features analyzed: 20
   • Best accuracy achieved: 0.9492
   • Analysis timestamp: 2025-07-21T02:31:44.887204