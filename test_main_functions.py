#!/usr/bin/env python3
"""
Test script to verify that all main functions save visualizations to results/visualizations/
"""

import sys
import os
from pathlib import Path
import subprocess
import time

def check_visualization_directory():
    """Check current state of visualization directory"""
    viz_dir = Path("results/visualizations")
    
    if not viz_dir.exists():
        print("📁 Creating results/visualizations directory...")
        viz_dir.mkdir(parents=True, exist_ok=True)
        return []
    
    files = list(viz_dir.glob("*.png"))
    return [f.name for f in files]

def test_data_processor_main():
    """Test data_processor.py main function"""
    print("\n🔄 TESTING: python src/data_processor.py")
    print("-" * 50)
    
    before_files = check_visualization_directory()
    print(f"📊 Files before: {len(before_files)}")
    
    try:
        # Run data processor main
        result = subprocess.run([
            sys.executable, "src/data_processor.py"
        ], capture_output=True, text=True, timeout=120)
        
        if result.returncode == 0:
            print("✅ data_processor.py executed successfully")
        else:
            print(f"❌ data_processor.py failed with return code {result.returncode}")
            print(f"Error: {result.stderr}")
        
        # Check files after
        after_files = check_visualization_directory()
        new_files = set(after_files) - set(before_files)
        
        print(f"📊 Files after: {len(after_files)}")
        print(f"📈 New files: {len(new_files)}")
        
        if new_files:
            print("🆕 New visualization files:")
            for file in sorted(new_files):
                print(f"  ✅ {file}")
        
        return len(new_files) > 0
        
    except subprocess.TimeoutExpired:
        print("⏰ data_processor.py timed out")
        return False
    except Exception as e:
        print(f"❌ Error running data_processor.py: {e}")
        return False

def test_classification_main():
    """Test bias_corrected_title_classifier.py main function"""
    print("\n🧠 TESTING: python src/bias_corrected_title_classifier.py")
    print("-" * 50)
    
    # Check if required input file exists
    input_file = Path("dataset/processed/weekly_merged_dataset_with_gamification.csv")
    if not input_file.exists():
        print("❌ Required input file not found. Skipping classification test.")
        return False
    
    before_files = check_visualization_directory()
    print(f"📊 Files before: {len(before_files)}")
    
    try:
        # Run classification main
        result = subprocess.run([
            sys.executable, "src/bias_corrected_title_classifier.py"
        ], capture_output=True, text=True, timeout=120)
        
        if result.returncode == 0:
            print("✅ bias_corrected_title_classifier.py executed successfully")
        else:
            print(f"❌ bias_corrected_title_classifier.py failed with return code {result.returncode}")
            print(f"Error: {result.stderr}")
        
        # Check files after
        after_files = check_visualization_directory()
        new_files = set(after_files) - set(before_files)
        
        print(f"📊 Files after: {len(after_files)}")
        print(f"📈 New files: {len(new_files)}")
        
        if new_files:
            print("🆕 New visualization files:")
            for file in sorted(new_files):
                print(f"  ✅ {file}")
        
        return len(new_files) > 0
        
    except subprocess.TimeoutExpired:
        print("⏰ bias_corrected_title_classifier.py timed out")
        return False
    except Exception as e:
        print(f"❌ Error running bias_corrected_title_classifier.py: {e}")
        return False

def test_feature_filter_main():
    """Test feature_filter2.py main function"""
    print("\n🔍 TESTING: python src/feature_filter2.py")
    print("-" * 50)
    
    # Check if required input file exists
    input_file = Path("dataset/processed/fatigue_classified.csv")
    if not input_file.exists():
        print("❌ Required input file not found. Skipping feature filter test.")
        return False
    
    before_files = check_visualization_directory()
    print(f"📊 Files before: {len(before_files)}")
    
    try:
        # Run feature filter main
        result = subprocess.run([
            sys.executable, "src/feature_filter2.py"
        ], capture_output=True, text=True, timeout=60)
        
        if result.returncode == 0:
            print("✅ feature_filter2.py executed successfully")
        else:
            print(f"❌ feature_filter2.py failed with return code {result.returncode}")
            print(f"Error: {result.stderr}")
        
        # Check files after
        after_files = check_visualization_directory()
        new_files = set(after_files) - set(before_files)
        
        print(f"📊 Files after: {len(after_files)}")
        print(f"📈 New files: {len(new_files)}")
        
        if new_files:
            print("🆕 New visualization files:")
            for file in sorted(new_files):
                print(f"  ✅ {file}")
        
        return len(new_files) > 0
        
    except subprocess.TimeoutExpired:
        print("⏰ feature_filter2.py timed out")
        return False
    except Exception as e:
        print(f"❌ Error running feature_filter2.py: {e}")
        return False

def main():
    """Test all main functions"""
    print("🧪 TESTING ALL MAIN FUNCTIONS")
    print("=" * 60)
    print("Verifying that all main() functions save visualizations to results/visualizations/")
    print("=" * 60)
    
    # Initial state
    initial_files = check_visualization_directory()
    print(f"📊 Initial files in results/visualizations/: {len(initial_files)}")
    
    # Test results
    results = {}
    
    # Test data processor
    results['data_processor'] = test_data_processor_main()
    
    # Test classification (depends on data processor)
    results['classification'] = test_classification_main()
    
    # Test feature filter (depends on classification)
    results['feature_filter'] = test_feature_filter_main()
    
    # Final summary
    print("\n" + "=" * 60)
    print("🎯 FINAL RESULTS")
    print("=" * 60)
    
    final_files = check_visualization_directory()
    print(f"📊 Final files in results/visualizations/: {len(final_files)}")
    
    print("\n📋 Test Results:")
    for test_name, success in results.items():
        status = "✅ PASS" if success else "❌ FAIL"
        print(f"  {status} {test_name}")
    
    print(f"\n📁 All files in results/visualizations/:")
    for file in sorted(final_files):
        file_path = Path("results/visualizations") / file
        size_mb = file_path.stat().st_size / (1024 * 1024)
        print(f"  📊 {file} ({size_mb:.2f} MB)")
    
    # Overall result
    all_passed = all(results.values())
    if all_passed:
        print(f"\n🎉 SUCCESS: All main functions save visualizations to results/visualizations/")
    else:
        failed_tests = [name for name, success in results.items() if not success]
        print(f"\n⚠️  WARNING: Some tests failed: {', '.join(failed_tests)}")
    
    print("\n✅ Testing completed!")

if __name__ == "__main__":
    main()
