#!/usr/bin/env python3
"""
All Visualizations Demo
Comprehensive demonstration of all visualization features added to the project files.

This script shows how to use all the visualization methods that have been integrated
directly into the project files without needing separate visualizer files.
"""

import sys
import os
from pathlib import Path
sys.path.append('src')

def demo_data_processor_visualizations():
    """Demonstrate data_processor.py visualizations"""
    print("🔄 DATA PROCESSOR VISUALIZATIONS")
    print("=" * 60)
    
    try:
        from data_processor import DataProcessor
        
        processor = DataProcessor()
        
        # Check if processed data exists
        processed_file = processor.processed_data_path / "weekly_merged_dataset_with_gamification.csv"
        
        if processed_file.exists():
            print("✅ Loading existing processed data...")
            import pandas as pd
            data = pd.read_csv(processed_file)
        else:
            print("⚙️ Processing raw data...")
            data = processor.process_all()
        
        print(f"📊 Dataset shape: {data.shape}")
        
        # Individual visualizations
        print("\n📈 Creating individual visualizations...")
        processor.visualize_data_overview(data, save_plots=True)
        processor.visualize_correlation_matrix(data, save_plots=True)
        processor.visualize_time_series(data, save_plots=True)
        processor.visualize_user_analysis(data, save_plots=True)
        processor.visualize_gamification_analysis(data, save_plots=True)
        
        # Data quality report
        print("\n📋 Generating data quality report...")
        processor.generate_data_quality_report(data)
        
        print("✅ Data processor visualizations completed!")
        
    except Exception as e:
        print(f"❌ Error in data processor demo: {e}")

def demo_classification_visualizations():
    """Demonstrate bias_corrected_title_classifier.py visualizations"""
    print("\n🧠 CLASSIFICATION VISUALIZATIONS")
    print("=" * 60)
    
    try:
        from bias_corrected_title_classifier import BiasCorrectedTitleClassifier
        import pandas as pd
        
        classifier = BiasCorrectedTitleClassifier()
        
        # Check if classification results exist
        classified_file = Path("dataset/processed/fatigue_classified.csv")
        
        if classified_file.exists():
            print("✅ Loading existing classification results...")
            data = pd.read_csv(classified_file)
        else:
            print("⚙️ Running bias-corrected classification...")
            input_file = "dataset/processed/weekly_merged_dataset_with_gamification.csv"
            if Path(input_file).exists():
                data = classifier.process_bias_corrected_classification(input_file)
            else:
                print("❌ Input file not found for classification")
                return
        
        print(f"📊 Classification dataset shape: {data.shape}")
        
        # Classification visualizations
        print("\n📊 Creating classification visualizations...")
        classifier.visualize_classification_results(data, save_plots=True)
        classifier.visualize_feature_analysis(data, save_plots=True)
        
        # Classification report
        print("\n📋 Generating classification report...")
        classifier.generate_classification_report(data)
        
        print("✅ Classification visualizations completed!")
        
    except Exception as e:
        print(f"❌ Error in classification demo: {e}")

def demo_feature_filtering_visualizations():
    """Demonstrate feature_filter2.py visualizations"""
    print("\n🔍 FEATURE FILTERING VISUALIZATIONS")
    print("=" * 60)
    
    try:
        from feature_filter2 import FeatureFilter2
        
        filter_obj = FeatureFilter2()
        
        # Check if classification results exist for filtering
        input_file = "dataset/processed/fatigue_classified.csv"
        output_file = "dataset/processed/safe_ml_bias_corrected_dataset.csv"
        
        if Path(input_file).exists():
            print("✅ Input file found for feature filtering...")
            
            # Create comprehensive filtering report with visualizations
            print("\n🔧 Creating comprehensive filtering analysis...")
            filter_obj.create_comprehensive_filtering_report(
                input_file, output_file, 
                target_column='corrected_fatigue_risk', 
                save_plots=True
            )
            
            print("✅ Feature filtering visualizations completed!")
            
        else:
            print("❌ Input file not found for feature filtering")
            print("Please run classification demo first")
        
    except Exception as e:
        print(f"❌ Error in feature filtering demo: {e}")

def demo_rfe_visualizations():
    """Demonstrate rfe_ablation_study.py visualizations"""
    print("\n📊 RFE ABLATION STUDY VISUALIZATIONS")
    print("=" * 60)
    
    try:
        from rfe_ablation_study import RFEAblationStudy
        
        # Check if safe dataset exists for RFE
        safe_datasets = [
            {
                'path': 'dataset/processed/safe_ml_bias_corrected_dataset.csv',
                'target': 'corrected_fatigue_risk',
                'name': 'Bias-Corrected Safe Dataset'
            }
        ]
        
        selected_dataset = None
        for dataset in safe_datasets:
            if Path(dataset['path']).exists():
                selected_dataset = dataset
                break
        
        if selected_dataset:
            print(f"✅ Using dataset: {selected_dataset['name']}")
            
            # Initialize RFE study
            study = RFEAblationStudy(
                data_path=selected_dataset['path'],
                target_column=selected_dataset['target'],
                random_state=42
            )
            
            # Load data
            study.load_data()
            
            # Run RFE study (simplified version for demo)
            print("\n⚙️ Running simplified RFE analysis...")
            study_results = study.run_complete_rfe_study()
            
            # Create visualizations
            print("\n📊 Creating RFE visualizations...")
            study.create_comprehensive_rfe_report(study_results, save_plots=True)
            
            print("✅ RFE visualizations completed!")
            
        else:
            print("❌ No safe dataset found for RFE analysis")
            print("Please run feature filtering demo first")
        
    except Exception as e:
        print(f"❌ Error in RFE demo: {e}")

def show_generated_files():
    """Show all generated visualization files"""
    print("\n📁 GENERATED VISUALIZATION FILES")
    print("=" * 60)
    
    # Check results/visualizations files
    viz_dir = Path("results/visualizations")
    if viz_dir.exists():
        print("📂 results/visualizations/:")
        for file in viz_dir.glob("*.png"):
            print(f"  ✅ {file.name}")
    
    # Check results directory
    results_dir = Path("results")
    if results_dir.exists():
        print("\n📂 results/:")
        for subdir in results_dir.iterdir():
            if subdir.is_dir():
                print(f"  📁 {subdir.name}/:")
                for file in subdir.glob("*.png"):
                    print(f"    ✅ {file.name}")

def main():
    """Main function to run all visualization demos"""
    print("🎨 COMPREHENSIVE VISUALIZATION DEMO")
    print("=" * 80)
    print("This demo shows all visualization features integrated into project files")
    print("=" * 80)
    
    # Run all demos
    demo_data_processor_visualizations()
    demo_classification_visualizations()
    demo_feature_filtering_visualizations()
    # demo_rfe_visualizations()  # Commented out as it takes long time
    
    # Show generated files
    show_generated_files()
    
    print("\n" + "=" * 80)
    print("🎉 ALL VISUALIZATION DEMOS COMPLETED!")
    print("=" * 80)
    print("📋 Summary of what was demonstrated:")
    print("  ✅ Data processing visualizations (5 types)")
    print("  ✅ Classification analysis visualizations (2 types)")
    print("  ✅ Feature filtering visualizations (2 types)")
    print("  ⏸️ RFE analysis visualizations (commented out - takes time)")
    print("\n📁 Check the following directories for output files:")
    print("  • results/visualizations/ - All visualization files")
    print("  • results/ - Other analysis result files")
    print("\n📖 For detailed usage instructions, see:")
    print("  • VISUALIZATION_README.md")
    print("  • COMPREHENSIVE_VISUALIZATION_SUMMARY.md")

if __name__ == "__main__":
    main()
