"""
Complete Analysis Pipeline
Combines Physical Activity Analysis + Fatigue Prediction with Model Accuracy
"""

import logging
import sys
import argparse
from pathlib import Path
import pandas as pd

# Add src to path
sys.path.append('src')

from data_processor import DataProcessor
from visualizer import Visualizer
from fatigue_classifier import FatigueRiskClassifier
from clean_ablation_study import AblationStudy


# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('analysis.log'),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger(__name__)

class CompleteAnalysisPipeline:
    """
    Complete analysis pipeline for fatigue prediction
    """

    def __init__(self, include_ml=True):
        """Initialize pipeline components"""
        self.include_ml = include_ml
        self.data_processor = DataProcessor(raw_data_path="dataset/raw")
        self.visualizer = Visualizer()
        
        if include_ml:
            self.fatigue_classifier = FatigueRiskClassifier()
        
        # Ensure output directories exist
        Path("results/reports").mkdir(parents=True, exist_ok=True)
        Path("results/visualizations").mkdir(parents=True, exist_ok=True)
        Path("logs").mkdir(parents=True, exist_ok=True)
    
    def run_data_processing(self):
        """Run data processing only"""
        logger.info("="*60)
        logger.info("PHASE 1: DATA PROCESSING")
        logger.info("="*60)

        # Step 1: Data Processing
        logger.info("Step 1: Processing raw data...")
        processed_data = self.data_processor.process_all()
        logger.info(f"✅ Data processing completed. Shape: {processed_data.shape}")

        # Step 2: Basic Visualization
        logger.info("Step 2: Creating basic visualizations...")
        self.visualizer.create_basic_visualizations(processed_data)
        logger.info("✅ Basic visualizations completed")

        return processed_data
    
    def run_fatigue_prediction(self, processed_data):
        """Run fatigue prediction with model accuracy"""
        logger.info("="*60)
        logger.info("PHASE 2: FATIGUE RISK PREDICTION WITH ML MODELS")
        logger.info("="*60)
        
        # Step 1: Fatigue Classification
        logger.info("Step 1: Running fatigue classification...")
        classified_data = self.fatigue_classifier.process_fatigue_classification(
            'dataset/processed/weekly_merged_dataset_with_gamification.csv'
        )
        summary = self.fatigue_classifier.generate_classification_summary(classified_data)
        logger.info(f"✅ Fatigue classification completed. Distribution: {summary['fatigue_percentages']}")
        
        # Step 2: Advanced ML Pipeline
        logger.info("Step 2: Running advanced ML pipeline...")
        
        # Check if bias corrected data exists
        bias_corrected_path = "dataset/processed/bias_corrected_fatigue_classified_cleaned.csv"
        if Path(bias_corrected_path).exists():
            logger.info("Step 2a: Running ablation study...")
            ablation_study = AblationStudy(
                data_path=bias_corrected_path,
                target_column='corrected_fatigue_risk',
                random_state=42
            )
            ablation_study.load_data()
            importance_df, optimal_result = ablation_study.run_complete_ablation_study()
            
            logger.info("Step 2b: Advanced model step skipped (clean_advanced_model removed)")
            # Advanced model functionality removed
            advanced_model.load_data()
            advanced_result, pipeline, encoder = advanced_model.run_complete_optimization()
            
            return {
                'classification_summary': summary,
                'ablation_result': optimal_result,
                'advanced_result': advanced_result,
                'best_accuracy': optimal_result['accuracy_mean'],
                'best_model': 'Ablation_Study_Optimal'
            }
        else:
            logger.warning(f"Bias corrected data not found at {bias_corrected_path}")
            return {
                'classification_summary': summary,
                'best_accuracy': None,
                'best_model': 'Classification_Only'
            }
    
    def run_complete_pipeline(self):
        """Execute complete analysis pipeline"""
        logger.info("🚀 STARTING COMPLETE ANALYSIS PIPELINE")
        logger.info("Includes: Data Processing + Fatigue Prediction + ML Models")
        logger.info("="*80)

        try:
            # Phase 1: Data Processing
            processed_data = self.run_data_processing()

            # Phase 2: Fatigue Prediction (if enabled)
            ml_results = None
            if self.include_ml:
                ml_results = self.run_fatigue_prediction(processed_data)

            # Final Summary
            self._print_final_summary(processed_data, ml_results)
            
            logger.info("="*80)
            logger.info("🎉 COMPLETE PIPELINE FINISHED SUCCESSFULLY!")
            logger.info("="*80)
            
            return {
                'ml_results': ml_results,
                'processed_data': processed_data
            }
            
        except Exception as e:
            logger.error(f"Pipeline failed: {str(e)}")
            raise
    
    def _print_final_summary(self, data, ml_results):
        """Print comprehensive final summary"""

        print("\n" + "="*80)
        print("🎉 COMPLETE ANALYSIS PIPELINE SUMMARY")
        print("="*80)

        # Data Processing Summary
        print(f"\n📊 DATA PROCESSING:")
        print(f"   • {len(data)} weekly observations processed")
        print(f"   • {data['identity'].nunique() if 'identity' in data.columns else 'N/A'} unique participants")
        
        # ML Results Summary
        if ml_results:
            print(f"\n🤖 FATIGUE PREDICTION & ML MODELS:")
            if 'classification_summary' in ml_results:
                dist = ml_results['classification_summary']['fatigue_percentages']
                print(f"   • Fatigue Risk Distribution:")
                print(f"     - High Risk: {dist.get('high_risk', 0):.1f}%")
                print(f"     - Medium Risk: {dist.get('medium_risk', 0):.1f}%")
                print(f"     - Low Risk: {dist.get('low_risk', 0):.1f}%")
            
            if ml_results.get('best_accuracy'):
                print(f"   • Best ML Model: {ml_results['best_model']}")
                print(f"   • Best Accuracy: {ml_results['best_accuracy']:.4f} ({ml_results['best_accuracy']*100:.2f}%)")
                
                if 'advanced_result' in ml_results:
                    adv = ml_results['advanced_result']
                    print(f"   • Advanced Model: {adv['accuracy_mean']:.4f} ({adv['accuracy_mean']*100:.2f}%)")
                    if 'f1_mean' in adv:
                        print(f"   • F1-Score: {adv['f1_mean']:.4f}")
        
        # Files Generated
        print(f"\n📁 FILES GENERATED:")
        print(f"   • dataset/processed/weekly_merged_dataset_with_gamification.csv")
        print(f"   • results/visualizations/*.png")
        if ml_results:
            print(f"   • dataset/processed/fatigue_risk_classified_dataset.csv")
            if ml_results.get('best_accuracy'):
                print(f"   • results/clean_production_model/*.pkl")
        
        print(f"\n✅ All analyses completed successfully!")
        print("="*80)


def main():
    """Main function with argument parsing"""
    parser = argparse.ArgumentParser(description='Complete Analysis Pipeline')
    parser.add_argument('--no-ml', action='store_true',
                       help='Skip machine learning components (data processing only)')
    parser.add_argument('--ml-only', action='store_true',
                       help='Run only machine learning pipeline')
    
    args = parser.parse_args()
    
    try:
        # Check if raw data exists (unless ml-only)
        if not args.ml_only:
            raw_data_path = Path("dataset/raw")
            if not raw_data_path.exists():
                print("❌ Raw data directory not found!")
                print("Please ensure dataset/raw/ contains strava.csv and pomokit.csv")
                return
            
            strava_file = raw_data_path / "strava.csv"
            pomokit_file = raw_data_path / "pomokit.csv"
            
            if not strava_file.exists() or not pomokit_file.exists():
                print("❌ Required data files not found!")
                print(f"Expected files:")
                print(f"  - {strava_file}")
                print(f"  - {pomokit_file}")
                return
        
        # Run appropriate pipeline
        if args.ml_only:
            # Run only ML pipeline
            from src.main_clean_pipeline import FatiguePredictionPipeline
            pipeline = FatiguePredictionPipeline(
                data_path='dataset/processed/bias_corrected_fatigue_classified_cleaned.csv',
                target_column='corrected_fatigue_risk',
                random_state=42
            )
            results = pipeline.run_complete_pipeline()
            print(f"\n🎉 ML Pipeline Completed!")
            print(f"📋 Results: {results}")
        else:
            # Run complete pipeline
            include_ml = not args.no_ml
            pipeline = CompleteAnalysisPipeline(include_ml=include_ml)
            pipeline.run_complete_pipeline()
        
    except KeyboardInterrupt:
        print("\n❌ Pipeline interrupted by user")
    except Exception as e:
        print(f"\n❌ Pipeline failed: {str(e)}")
        logger.error(f"Pipeline failed: {str(e)}", exc_info=True)


if __name__ == "__main__":
    main()
