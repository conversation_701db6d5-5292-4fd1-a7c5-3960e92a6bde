algorithm,algorithm_name,n_features_selected,selected_features,accuracy_mean,accuracy_std,f1_macro_mean,f1_macro_std,precision_macro_mean,recall_macro_mean,train_accuracy_mean,overfitting_score,status
logistic_regression,Logistic Regression,5,"['pomokit_title_count', 'total_cycles', 'consistency_score', 'work_days', 'title_balance_ratio']",0.5016949152542373,0.070749880902131,0.513300433559363,0.07352977627608752,0.5047824147201145,0.6139080459770115,0.507795619357703,0.006100704103465748,success
logistic_regression,Logistic Regression,10,"['strava_title_length', 'pomokit_title_length', 'gamification_balance', 'pomokit_title_count', 'total_cycles', 'consistency_score', 'achievement_rate', 'work_days', 'pomokit_unique_words', 'title_balance_ratio']",0.6189362945645821,0.07722464875420029,0.6247124656828157,0.0729636665114429,0.6177495214337319,0.666360153256705,0.7165125055498002,0.09757621098521807,success
logistic_regression,Logistic Regression,15,"['strava_title_length', 'pomokit_title_length', 'gamification_balance', 'productivity_points', 'pomokit_title_count', 'total_title_diversity', 'total_cycles', 'activity_days', 'consistency_score', 'achievement_rate', 'total_distance_km', 'avg_distance_km', 'work_days', 'pomokit_unique_words', 'title_balance_ratio']",0.6223845704266511,0.08207762988946464,0.6250402080769483,0.07530915299454966,0.6177316781754346,0.6622860791826308,0.7225025899067634,0.10011801948011234,success
logistic_regression,Logistic Regression,20,"['strava_title_length', 'pomokit_title_length', 'strava_title_count', 'gamification_balance', 'activity_points', 'productivity_points', 'total_time_minutes', 'pomokit_title_count', 'total_title_diversity', 'total_cycles', 'activity_days', 'consistency_score', 'achievement_rate', 'total_distance_km', 'avg_distance_km', 'work_days', 'avg_time_minutes', 'strava_unique_words', 'pomokit_unique_words', 'title_balance_ratio']",0.6154880187025131,0.07590325770579145,0.6188249718848833,0.06839030054344496,0.6122820841964288,0.6515453384418902,0.7310936806274974,0.11560566192498423,success
random_forest,Random Forest,5,"['strava_title_length', 'productivity_points', 'total_cycles', 'consistency_score', 'work_days']",0.9347749853886616,0.029464458572343255,0.9193383660465086,0.03832804385684983,0.9191424226908097,0.92301404853129,0.9793806422968773,0.04460565690821572,success
random_forest,Random Forest,10,"['strava_title_length', 'strava_title_count', 'productivity_points', 'pomokit_title_count', 'total_cycles', 'activity_days', 'consistency_score', 'achievement_rate', 'total_distance_km', 'work_days']",0.9415546464056106,0.040245047802277174,0.9256187233898295,0.05570942093027523,0.9224756222642763,0.9322733077905492,1.0,0.058445353594389404,success
random_forest,Random Forest,15,"['strava_title_length', 'pomokit_title_length', 'strava_title_count', 'gamification_balance', 'productivity_points', 'pomokit_title_count', 'total_title_diversity', 'total_cycles', 'activity_days', 'consistency_score', 'achievement_rate', 'total_distance_km', 'work_days', 'strava_unique_words', 'title_balance_ratio']",0.948509643483343,0.028714101699021864,0.9351806537581249,0.034850481711406325,0.9307237675053767,0.9437547892720307,1.0,0.05149035651665701,success
random_forest,Random Forest,20,"['strava_title_length', 'pomokit_title_length', 'strava_title_count', 'gamification_balance', 'activity_points', 'productivity_points', 'total_time_minutes', 'pomokit_title_count', 'total_title_diversity', 'total_cycles', 'activity_days', 'consistency_score', 'achievement_rate', 'total_distance_km', 'avg_distance_km', 'work_days', 'avg_time_minutes', 'strava_unique_words', 'pomokit_unique_words', 'title_balance_ratio']",0.9451198129748685,0.027261734788721143,0.9302057644275188,0.03308656640795332,0.9276081559989606,0.937088122605364,1.0,0.05488018702513153,success
gradient_boosting,Gradient Boosting,5,"['pomokit_title_length', 'productivity_points', 'pomokit_title_count', 'total_cycles', 'consistency_score']",0.9553477498538866,0.027972925594107252,0.9459454888173264,0.033631123929990715,0.9376963394204774,0.9629374201787995,0.9733572591386711,0.01800950928478451,success
gradient_boosting,Gradient Boosting,10,"['strava_title_length', 'pomokit_title_length', 'gamification_balance', 'productivity_points', 'pomokit_title_count', 'total_cycles', 'consistency_score', 'total_distance_km', 'work_days', 'title_balance_ratio']",0.9244886031560491,0.050492726637318235,0.8975749100070946,0.07168916072712987,0.9040802109767627,0.8974584929757343,1.0,0.07551139684395092,success
gradient_boosting,Gradient Boosting,15,"['strava_title_length', 'pomokit_title_length', 'gamification_balance', 'productivity_points', 'total_time_minutes', 'pomokit_title_count', 'total_title_diversity', 'total_cycles', 'consistency_score', 'achievement_rate', 'total_distance_km', 'avg_distance_km', 'work_days', 'pomokit_unique_words', 'title_balance_ratio']",0.9209234365867914,0.03877205025382182,0.8924914902048098,0.05286656107675285,0.9018570691554564,0.8896040868454662,1.0,0.07907656341320857,success
gradient_boosting,Gradient Boosting,20,"['strava_title_length', 'pomokit_title_length', 'strava_title_count', 'gamification_balance', 'activity_points', 'productivity_points', 'total_time_minutes', 'pomokit_title_count', 'total_title_diversity', 'total_cycles', 'activity_days', 'consistency_score', 'achievement_rate', 'total_distance_km', 'avg_distance_km', 'work_days', 'avg_time_minutes', 'strava_unique_words', 'pomokit_unique_words', 'title_balance_ratio']",0.9278199883109293,0.03684236735880257,0.9035707200477484,0.050136027082916756,0.9095656349823017,0.9044189016602809,1.0,0.07218001168907073,success
svm,Support Vector Machine,5,"['productivity_points', 'pomokit_title_count', 'total_cycles', 'consistency_score', 'work_days']",0.5154880187025132,0.026939440954482186,0.4650092244145944,0.05386382864768333,0.37797705218757843,0.669655172413793,0.5257473730945685,0.010259354392055386,success
svm,Support Vector Machine,10,"['strava_title_length', 'pomokit_title_length', 'productivity_points', 'pomokit_title_count', 'total_title_diversity', 'total_cycles', 'consistency_score', 'work_days', 'pomokit_unique_words', 'title_balance_ratio']",0.635885447106955,0.051525139986978115,0.639508540020595,0.045669974552268615,0.6433655384604151,0.7203065134099617,0.6726690839129791,0.036783636806024034,success
svm,Support Vector Machine,15,"['strava_title_length', 'pomokit_title_length', 'gamification_balance', 'productivity_points', 'pomokit_title_count', 'total_title_diversity', 'total_cycles', 'activity_days', 'consistency_score', 'achievement_rate', 'total_distance_km', 'avg_distance_km', 'work_days', 'pomokit_unique_words', 'title_balance_ratio']",0.6565166569257744,0.03915166947799978,0.6562403485384323,0.03717574376703878,0.6546178915884385,0.7198084291187741,0.7062009767648364,0.04968431983906196,success
svm,Support Vector Machine,20,"['strava_title_length', 'pomokit_title_length', 'strava_title_count', 'gamification_balance', 'activity_points', 'productivity_points', 'total_time_minutes', 'pomokit_title_count', 'total_title_diversity', 'total_cycles', 'activity_days', 'consistency_score', 'achievement_rate', 'total_distance_km', 'avg_distance_km', 'work_days', 'avg_time_minutes', 'strava_unique_words', 'pomokit_unique_words', 'title_balance_ratio']",0.6462887200467563,0.04974060139631366,0.6439882965086691,0.04857232810250288,0.6403438493234114,0.7075095785440614,0.7096381530264909,0.06334943297973461,success
xgboost,XGBoost,5,"['strava_title_count', 'gamification_balance', 'productivity_points', 'pomokit_title_count', 'consistency_score']",0.9554061952074809,0.031804477167276565,0.9463846396376023,0.03796814139311865,0.9372566939233605,0.9636781609195403,0.9604817226579844,0.0050755274505034365,success
xgboost,XGBoost,10,"['strava_title_length', 'strava_title_count', 'gamification_balance', 'activity_points', 'productivity_points', 'pomokit_title_count', 'total_cycles', 'activity_days', 'consistency_score', 'title_balance_ratio']",0.9381648158971361,0.0337415509450155,0.9209228995526457,0.04269637906781443,0.9200168350168351,0.9266411238825031,0.9991416309012877,0.06097681500415164,success
xgboost,XGBoost,15,"['strava_title_length', 'pomokit_title_length', 'strava_title_count', 'gamification_balance', 'activity_points', 'productivity_points', 'total_time_minutes', 'pomokit_title_count', 'total_title_diversity', 'total_cycles', 'activity_days', 'consistency_score', 'achievement_rate', 'total_distance_km', 'title_balance_ratio']",0.9278784336645236,0.03505571601936193,0.9106271641417296,0.041256995357485705,0.9125953059286391,0.9133077905491698,1.0,0.0721215663354764,success
xgboost,XGBoost,20,"['strava_title_length', 'pomokit_title_length', 'strava_title_count', 'gamification_balance', 'activity_points', 'productivity_points', 'total_time_minutes', 'pomokit_title_count', 'total_title_diversity', 'total_cycles', 'activity_days', 'consistency_score', 'achievement_rate', 'total_distance_km', 'avg_distance_km', 'work_days', 'avg_time_minutes', 'strava_unique_words', 'pomokit_unique_words', 'title_balance_ratio']",0.9209818819403857,0.04016756583539536,0.8935524926611,0.053297179573226765,0.901549823633157,0.8903448275862068,1.0,0.07901811805961434,success
