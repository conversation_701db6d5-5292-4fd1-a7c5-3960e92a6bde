================================================================================
🔍 RFE ABLATION STUDY REPORT
================================================================================

📋 DATASET INFORMATION:
   • Dataset Path: dataset\processed\safe_ml_fatigue_dataset.csv
   • Target Column: fatigue_risk
   • Total Features: 20
   • Total Samples: 291

🤖 ALGORITHMS TESTED:
   1. Logistic Regression (logistic_regression)
   2. Random Forest (random_forest)
   3. Gradient Boosting (gradient_boosting)
   4. Support Vector Machine (svm)
   5. XGBoost (xgboost)

📊 FEATURE COUNT RANGE TESTED:
   • Range: [5, 10, 15, 20, 25, 30]
   • Total Experiments: 20

🏆 BEST OVERALL PERFORMANCE:
   • Algorithm: XGBoost
   • Features Used: 5
   • Accuracy: 0.9554 ± 0.0318
   • F1-Score: 0.9464
   • ✅ Overfitting Risk: 0.0051 (LOW - Train-Test gap < 5%)

🎯 BEST PERFORMANCE BY ALGORITHM:
   • Logistic Regression:
     - Features: 15
     - Accuracy: 0.6224 ± 0.0821
     - F1-Score: 0.6250
     - ⚠️ Overfitting: 0.1001 (HIGH)
   • Random Forest:
     - Features: 15
     - Accuracy: 0.9485 ± 0.0287
     - F1-Score: 0.9352
     - ⚠️ Overfitting: 0.0515 (MODERATE)
   • Gradient Boosting:
     - Features: 5
     - Accuracy: 0.9553 ± 0.0280
     - F1-Score: 0.9459
     - ✅ Overfitting: 0.0180 (LOW)
   • Support Vector Machine:
     - Features: 15
     - Accuracy: 0.6565 ± 0.0392
     - F1-Score: 0.6562
     - ✅ Overfitting: 0.0497 (LOW)
   • XGBoost:
     - Features: 5
     - Accuracy: 0.9554 ± 0.0318
     - F1-Score: 0.9464
     - ✅ Overfitting: 0.0051 (LOW)

⭐ MOST FREQUENTLY SELECTED FEATURES:
    1. consistency_score: 20 times (100.0%)
    2. pomokit_title_count: 19 times (95.0%)
    3. total_cycles: 19 times (95.0%)
    4. productivity_points: 18 times (90.0%)
    5. work_days: 16 times (80.0%)
    6. strava_title_length: 16 times (80.0%)
    7. title_balance_ratio: 15 times (75.0%)
    8. pomokit_title_length: 14 times (70.0%)
    9. gamification_balance: 14 times (70.0%)
   10. achievement_rate: 12 times (60.0%)

🎖️ MOST CONSISTENT FEATURES (in top 10 results):
    1. productivity_points: 10/10 top results
    2. consistency_score: 10/10 top results
    3. pomokit_title_count: 9/10 top results
    4. total_cycles: 9/10 top results
    5. strava_title_length: 8/10 top results
    6. strava_title_count: 7/10 top results
    7. gamification_balance: 7/10 top results
    8. pomokit_title_length: 6/10 top results
    9. activity_days: 6/10 top results
   10. total_distance_km: 6/10 top results

🔍 OVERFITTING ANALYSIS:
   • Best Model Overfitting Score: 0.0051
   • ✅ LOW OVERFITTING RISK: Good generalization expected

   📊 Overfitting by Algorithm:
     ⚠️ Logistic Regression: 0.1001 (HIGH)
     ⚠️ Random Forest: 0.0515 (MODERATE)
     ✅ Gradient Boosting: 0.0180 (LOW)
     ✅ Support Vector Machine: 0.0497 (LOW)
     ✅ XGBoost: 0.0051 (LOW)

📊 COMPREHENSIVE FEATURE IMPORTANCE ANALYSIS:
   • Algorithm: XGBoost
   • Method: Permutation Importance + Ablation Analysis
   • Total Features: 5
   • Baseline Accuracy: 0.9554

   🎯 Feature Impact Analysis (Top 10):
      1. ⚪ gamification_balance:
         - Baseline: 0.9554 | Without: 0.9520
         - Impact: 0.0034 (0.36%)
         - MINIMAL - Negligible performance impact
      2. ⚪ strava_title_count:
         - Baseline: 0.9554 | Without: 0.9588
         - Impact: -0.0034 (-0.35%)
         - MINIMAL - Negligible performance impact
      3. ⚪ productivity_points:
         - Baseline: 0.9554 | Without: 0.9520
         - Impact: 0.0034 (0.35%)
         - MINIMAL - Negligible performance impact
      4. ⚪ pomokit_title_count:
         - Baseline: 0.9554 | Without: 0.9520
         - Impact: 0.0034 (0.35%)
         - MINIMAL - Negligible performance impact
      5. ⚪ consistency_score:
         - Baseline: 0.9554 | Without: 0.9520
         - Impact: 0.0034 (0.35%)
         - MINIMAL - Negligible performance impact

   📊 Permutation Importance Summary:
     • gamification_balance: 0.17% (score: 0.0010 ±0.0060)
     • strava_title_count: 2.65% (score: 0.0158 ±0.0074)
     • productivity_points: 32.93% (score: 0.1962 ±0.0168)
     • pomokit_title_count: 0.23% (score: -0.0014 ±0.0017)
     • consistency_score: 64.01% (score: 0.3814 ±0.0290)

✅ RECOMMENDED OPTIMAL FEATURE SET:
   • Algorithm: XGBoost
   • Number of Features: 5
   • Selected Features:
      1. consistency_score
      2. gamification_balance
      3. pomokit_title_count
      4. productivity_points
      5. strava_title_count
================================================================================