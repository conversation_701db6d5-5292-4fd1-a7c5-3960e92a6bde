"""
Complete Analysis Pipeline for Bias Corrected Title Classifier with RFE Feature Selection
Combines Physical Activity Analysis + Bias Corrected Fatigue Prediction with RFE-based Feature Selection

BASED ON MAIN2.PY STRUCTURE:
- Uses main2.py as reference template
- Replaces clean_ablation_study with rfe_ablation_study
- Integrates RFE with multiple algorithms (LR, RF, GB, SVM)
- Maintains same bias correction pipeline structure and modes
- Provides comprehensive RFE analysis with bias correction feature filtering

DESIGNED FOR BIAS_CORRECTED_TITLE_CLASSIFIER.PY:
- Integrates bias_corrected_title_classifier.py with feature_filter2.py
- Handles bias correction and context features
- Prevents data leakage from bias correction process
- Provides safe datasets for RFE training
- Supports multiple pipeline modes

PIPELINE MODES:
1. Complete Pipeline (default): Data Processing + Bias Corrected Fatigue Prediction + RFE ML
2. Bias Corrected Only (--bias-corrected-only): Bias Corrected Classification + Feature Filtering + RFE ML
3. No ML (--no-ml): Data Processing only
4. ML Only (--ml-only): RFE ML pipeline only

BIAS CORRECTION FEATURES:
- Language pattern detection (indonesian_dominant, english_dominant, mixed)
- Activity type detection (physical_dominant, work_dominant, balanced)
- Cultural bias adjustment in scoring
- Context-aware fatigue classification

RFE FEATURE FILTERING:
- Automatically removes bias correction features (corrected_*)
- Removes context features (language_pattern, activity_type)
- Prevents data leakage from bias correction process
- Uses RFE with multiple algorithms for optimal feature selection
- Provides feature importance analysis with permutation importance
"""

import logging
import sys
import argparse
from pathlib import Path
# pandas will be imported when needed

# Add src to path
sys.path.append('src')

from data_processor import DataProcessor
from visualizer import Visualizer
from bias_corrected_title_classifier import BiasCorrectedTitleClassifier
from feature_filter2 import FeatureFilter2
from rfe_ablation_study import RFEAblationStudy


# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('analysis.log'),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger(__name__)

class BiasCorrectedRFEAnalysisPipeline:
    """
    Complete analysis pipeline for bias-corrected fatigue prediction with RFE feature selection
    """

    def __init__(self, include_ml=True):
        """Initialize pipeline components"""
        self.include_ml = include_ml
        self.data_processor = DataProcessor(raw_data_path="dataset/raw")
        self.visualizer = Visualizer()

        if include_ml:
            self.bias_corrected_classifier = BiasCorrectedTitleClassifier()
            self.feature_filter = FeatureFilter2()

        # Ensure output directories exist
        Path("results/reports").mkdir(parents=True, exist_ok=True)
        Path("results/visualizations").mkdir(parents=True, exist_ok=True)
        Path("logs").mkdir(parents=True, exist_ok=True)
    
    def run_data_processing(self):
        """Run data processing with visualizations"""
        logger.info("="*60)
        logger.info("PHASE 1: DATA PROCESSING WITH VISUALIZATIONS")
        logger.info("="*60)

        # Step 1: Data Processing
        logger.info("Step 1: Processing raw data...")
        processed_data = self.data_processor.process_all()
        logger.info(f"✅ Data processing completed. Shape: {processed_data.shape}")

        # Step 2: Create Data Processing Visualizations
        logger.info("Step 2: Creating data processing visualizations...")
        self.data_processor.create_comprehensive_report(processed_data, save_plots=True)
        logger.info("✅ Data processing visualizations saved to results/visualizations/")

        return processed_data
    
    def run_bias_corrected_prediction(self, processed_data=None):
        """Run bias corrected fatigue prediction with RFE feature filtering and model accuracy"""
        logger.info("="*60)
        logger.info("PHASE 2: BIAS CORRECTED FATIGUE PREDICTION WITH RFE FEATURE SELECTION & ML MODELS")
        logger.info("="*60)

        # Step 1: Bias Corrected Classification
        logger.info("Step 1: Running bias corrected fatigue classification...")
        classified_data = self.bias_corrected_classifier.process_bias_corrected_classification(
            'dataset/processed/weekly_merged_dataset_with_gamification.csv'
        )

        # Save classified data
        classified_output = "dataset/processed/fatigue_classified.csv"
        classified_data.to_csv(classified_output, index=False)

        summary = self._generate_bias_corrected_summary(classified_data)
        logger.info(f"✅ Bias corrected classification completed. Distribution: {summary['fatigue_percentages']}")

        # Step 1b: Create Classification Visualizations
        logger.info("Step 1b: Creating classification visualizations...")
        self.bias_corrected_classifier.create_comprehensive_classification_report(classified_data, save_plots=True)
        logger.info("✅ Classification visualizations saved to results/visualizations/")

        # Step 2: Feature Filtering for ML Safety (Bias Correction Specific)
        logger.info("Step 2: Applying bias correction feature filtering to prevent data leakage...")
        safe_output = "dataset/processed/safe_ml_bias_corrected_dataset.csv"

        safe_path = self.feature_filter.create_safe_dataset_for_bias_corrected_classifier(
            classified_output,
            safe_output,
            target_column='corrected_fatigue_risk'
        )
        logger.info(f"✅ Bias correction feature filtering completed. Safe dataset: {safe_path}")

        # Step 2b: Create Feature Filtering Visualizations
        logger.info("Step 2b: Creating feature filtering visualizations...")
        self.feature_filter.create_comprehensive_filtering_report(
            classified_output, safe_output, target_column='corrected_fatigue_risk', save_plots=True
        )
        logger.info("✅ Feature filtering visualizations saved to results/visualizations/")

        # Step 3: Analyze Bias Correction Impact
        logger.info("Step 3: Analyzing bias correction impact...")
        bias_analysis = self.feature_filter.analyze_bias_correction_impact(classified_data)
        logger.info(f"✅ Bias correction analysis completed. "
                   f"Found {len(bias_analysis['bias_correction_features'])} bias correction features")

        # Step 4: RFE ML Pipeline with Safe Dataset
        logger.info("Step 4: Running RFE ML pipeline with bias correction safe features...")

        # Use safe dataset for ML training with auto-detection
        # Check which safe datasets are available (prioritize current pipeline's output)
        safe_datasets = [
            {
                'path': safe_output,  # Current pipeline output (highest priority)
                'target': 'corrected_fatigue_risk',
                'name': 'Current Pipeline Output (Bias-Corrected)',
                'priority': 1
            },
            {
                'path': 'dataset/processed/safe_ml_fatigue_dataset.csv',
                'target': 'fatigue_risk',
                'name': 'Regular Fatigue Classification',
                'priority': 2
            },
            {
                'path': 'dataset/processed/safe_ml_title_only_dataset.csv',
                'target': 'title_fatigue_risk',
                'name': 'Title-Only Fatigue Classification',
                'priority': 3
            }
        ]

        # Find the first available safe dataset
        selected_dataset = None
        for dataset in sorted(safe_datasets, key=lambda x: x['priority']):
            if Path(dataset['path']).exists():
                selected_dataset = dataset
                break

        if selected_dataset:
            logger.info(f"📁 Using safe dataset: {selected_dataset['path']}")
            logger.info(f"🎯 Target column: {selected_dataset['target']}")
            logger.info(f"📋 Dataset type: {selected_dataset['name']}")

            logger.info("Step 4a: Running RFE ablation study with bias correction safe features...")
            rfe_study = RFEAblationStudy(
                data_path=selected_dataset['path'],
                target_column=selected_dataset['target'],
                random_state=42
            )
            rfe_study.load_data()
            study_results = rfe_study.run_complete_rfe_study()

            # Save RFE study results
            results_file, report_file = rfe_study.save_results(study_results)
            logger.info(f"✅ RFE ablation study results saved:")
            logger.info(f"   • Results: {results_file}")
            logger.info(f"   • Report: {report_file}")
            logger.info(f"   • RFE Analysis Report: results/rfe_ablation_study/rfe_report_*.txt")

            # Step 4b: Create RFE Visualizations
            logger.info("Step 4b: Creating RFE analysis visualizations...")
            rfe_study.create_comprehensive_rfe_report(study_results, save_plots=True)
            logger.info("✅ RFE visualizations saved to results/visualizations/")
            
            # Extract optimal result from RFE analysis
            optimal_result = study_results['analysis'].get('best_overall', {})
            if optimal_result:
                optimal_result['accuracy_mean'] = optimal_result.get('accuracy', 0.0)
                optimal_result['accuracy_std'] = optimal_result.get('accuracy_std', 0.0)

            logger.info("Step 4b: Advanced model step skipped (clean_advanced_model removed)")
            # Advanced model functionality removed
            advanced_result = {
                'accuracy_mean': 0.0,
                'f1_mean': 0.0,
                'precision_mean': 0.0,
                'recall_mean': 0.0
            }

            return {
                'classification_summary': summary,
                'bias_correction_analysis': bias_analysis,
                'safe_dataset_path': selected_dataset['path'],
                'safe_dataset_type': selected_dataset['name'],
                'target_column': selected_dataset['target'],
                'rfe_result': optimal_result,
                'rfe_study_results': study_results,
                'advanced_result': advanced_result,
                'best_accuracy': optimal_result.get('accuracy_mean', 0.0),
                'best_model': f"RFE_Study_Optimal_Bias_Corrected_Safe_Features_{optimal_result.get('algorithm_name', 'Unknown')}",
                'best_algorithm': optimal_result.get('algorithm_name', 'Unknown'),
                'optimal_features_count': optimal_result.get('n_features', 0),
                'data_leakage_prevented': True,
                'bias_correction_applied': True
            }
        else:
            logger.warning("❌ No safe dataset found!")
            logger.warning("Available safe datasets to check:")
            for dataset in safe_datasets:
                status = "✅ EXISTS" if Path(dataset['path']).exists() else "❌ NOT FOUND"
                logger.warning(f"   • {dataset['path']} - {status}")
            logger.warning("Please run the pipeline first to generate safe datasets")
            return {
                'classification_summary': summary,
                'bias_correction_analysis': bias_analysis,
                'safe_dataset_path': None,
                'best_accuracy': None,
                'best_model': 'Bias_Corrected_Classification_Only',
                'data_leakage_prevented': False,
                'bias_correction_applied': True
            }

    def run_bias_corrected_only_pipeline(self):
        """Execute bias corrected classification with RFE feature filtering only"""
        logger.info("🚀 STARTING BIAS CORRECTED FATIGUE CLASSIFICATION PIPELINE WITH RFE")
        logger.info("Includes: Bias Corrected Classification + Feature Filtering + RFE ML Models")
        logger.info("="*80)

        try:
            # Check if processed data exists
            processed_data_path = "dataset/processed/weekly_merged_dataset_with_gamification.csv"
            if not Path(processed_data_path).exists():
                logger.error(f"Processed data not found: {processed_data_path}")
                logger.error("Please run data processing first or use --complete flag")
                return None

            # Load processed data for summary
            import pandas as pd
            processed_data = pd.read_csv(processed_data_path)
            logger.info(f"✅ Loaded processed data: {processed_data.shape}")

            # Run bias corrected prediction with RFE feature filtering
            ml_results = self.run_bias_corrected_prediction(processed_data)

            # Create additional visualizations if not already created
            logger.info("Creating additional research-specific visualizations...")
            self._create_research_specific_visualizations(processed_data)

            # Print summary
            self._print_bias_corrected_only_summary(processed_data, ml_results)

            logger.info("="*80)
            logger.info("🎉 BIAS CORRECTED CLASSIFICATION PIPELINE WITH RFE FINISHED SUCCESSFULLY!")
            logger.info("="*80)

            return {
                'ml_results': ml_results,
                'processed_data': processed_data
            }

        except Exception as e:
            logger.error(f"Bias corrected pipeline failed: {str(e)}")
            raise

    def _generate_bias_corrected_summary(self, classified_data):
        """Generate summary for bias corrected classification"""
        import pandas as pd

        # Count fatigue risk distribution
        fatigue_counts = classified_data['corrected_fatigue_risk'].value_counts()
        total_count = len(classified_data)

        fatigue_percentages = {}
        for risk_level in ['high_risk', 'medium_risk', 'low_risk']:
            count = fatigue_counts.get(risk_level, 0)
            percentage = (count / total_count) * 100 if total_count > 0 else 0
            fatigue_percentages[risk_level] = percentage

        # Bias correction analysis
        bias_correction_summary = {}
        if 'language_pattern' in classified_data.columns:
            bias_correction_summary['language_patterns'] = classified_data['language_pattern'].value_counts().to_dict()
        if 'activity_type' in classified_data.columns:
            bias_correction_summary['activity_types'] = classified_data['activity_type'].value_counts().to_dict()

        return {
            'total_observations': total_count,
            'fatigue_counts': fatigue_counts.to_dict(),
            'fatigue_percentages': fatigue_percentages,
            'bias_correction_summary': bias_correction_summary
        }

    def _create_research_specific_visualizations(self, data):
        """Create additional research-specific visualizations"""
        try:
            import pandas as pd
            import matplotlib
            matplotlib.use('Agg')
            import matplotlib.pyplot as plt
            import seaborn as sns
            from pathlib import Path
            from scipy import stats
            import numpy as np
            import warnings
            warnings.filterwarnings('ignore')

            logger.info("Creating research-specific visualizations...")

            # Create visualization directory
            viz_dir = Path("results/visualizations")
            viz_dir.mkdir(parents=True, exist_ok=True)

            # Create regression analysis plot
            fig, axes = plt.subplots(2, 2, figsize=(15, 10))
            fig.suptitle('Research-Specific Analysis: Cardiovascular Activity & Productivity',
                        fontsize=16, fontweight='bold')

            # 1. Distance vs Cycles with regression line
            if 'total_distance_km' in data.columns and 'total_cycles' in data.columns:
                x = data['total_distance_km'].dropna()
                y = data['total_cycles'][x.index]

                if len(x) > 1:
                    slope, intercept, r_value, p_value, std_err = stats.linregress(x, y)
                    line = slope * x + intercept

                    axes[0, 0].scatter(x, y, alpha=0.6, color='blue')
                    axes[0, 0].plot(x, line, 'r-', linewidth=2)
                    axes[0, 0].set_title(f'Distance vs Productivity\nR² = {r_value**2:.3f}, p = {p_value:.3f}')
                    axes[0, 0].set_xlabel('Weekly Distance (km)')
                    axes[0, 0].set_ylabel('Weekly Productivity (cycles)')
                    axes[0, 0].grid(True, alpha=0.3)

            # 2. Achievement Rate Distribution
            if 'achievement_rate' in data.columns:
                achievement_clean = data['achievement_rate'].dropna()
                axes[0, 1].hist(achievement_clean, bins=20, alpha=0.7, color='green', edgecolor='black')
                axes[0, 1].axvline(achievement_clean.mean(), color='red', linestyle='--',
                                  label=f'Mean: {achievement_clean.mean():.3f}')
                axes[0, 1].set_title('Achievement Rate Distribution')
                axes[0, 1].set_xlabel('Achievement Rate')
                axes[0, 1].set_ylabel('Frequency')
                axes[0, 1].legend()
                axes[0, 1].grid(True, alpha=0.3)

            # 3. Activity Level vs Productivity
            if 'total_distance_km' in data.columns and 'total_cycles' in data.columns:
                # Create activity level categories
                data_copy = data.copy()
                data_copy['activity_level'] = pd.cut(data_copy['total_distance_km'],
                                                   bins=3, labels=['Low', 'Medium', 'High'])

                activity_productivity = data_copy.groupby('activity_level')['total_cycles'].agg(['mean', 'std']).reset_index()

                bars = axes[1, 0].bar(activity_productivity['activity_level'], activity_productivity['mean'],
                                     yerr=activity_productivity['std'], capsize=5, alpha=0.7, color='orange')
                axes[1, 0].set_title('Productivity by Activity Level')
                axes[1, 0].set_xlabel('Physical Activity Level')
                axes[1, 0].set_ylabel('Average Weekly Cycles')
                axes[1, 0].grid(True, alpha=0.3)

            # 4. Weekly Efficiency Distribution
            if 'weekly_efficiency' in data.columns:
                efficiency_clean = data['weekly_efficiency'].dropna()
                # Remove extreme outliers
                if len(efficiency_clean) > 0:
                    q1, q3 = efficiency_clean.quantile([0.25, 0.75])
                    iqr = q3 - q1
                    lower_bound = q1 - 1.5 * iqr
                    upper_bound = q3 + 1.5 * iqr
                    efficiency_clean = efficiency_clean[(efficiency_clean >= lower_bound) &
                                                      (efficiency_clean <= upper_bound)]

                    axes[1, 1].hist(efficiency_clean, bins=15, alpha=0.7, color='purple', edgecolor='black')
                    axes[1, 1].axvline(efficiency_clean.mean(), color='red', linestyle='--',
                                      label=f'Mean: {efficiency_clean.mean():.2f}')
                    axes[1, 1].set_title('Weekly Efficiency Distribution')
                    axes[1, 1].set_xlabel('Weekly Efficiency')
                    axes[1, 1].set_ylabel('Frequency')
                    axes[1, 1].legend()
                    axes[1, 1].grid(True, alpha=0.3)

            plt.tight_layout()

            # Save plot
            plot_path = viz_dir / 'research_specific_analysis.png'
            plt.savefig(plot_path, dpi=300, bbox_inches='tight')
            logger.info(f"✅ Saved research-specific analysis plot to {plot_path}")

            plt.close()

        except Exception as e:
            logger.warning(f"Could not create research-specific visualizations: {e}")

    def _print_bias_corrected_only_summary(self, data, ml_results):
        """Print summary for bias corrected only pipeline with RFE"""

        print("\n" + "="*80)
        print("🎉 BIAS CORRECTED FATIGUE CLASSIFICATION PIPELINE WITH RFE SUMMARY")
        print("="*80)

        # Data Summary
        print(f"\n📊 DATA SUMMARY:")
        print(f"   • {len(data)} weekly observations processed")
        print(f"   • {data['identity'].nunique() if 'identity' in data.columns else 'N/A'} unique participants")

        # Bias Correction Summary
        if ml_results and 'bias_correction_analysis' in ml_results:
            bias_analysis = ml_results['bias_correction_analysis']
            print(f"\n🔧 BIAS CORRECTION ANALYSIS:")
            print(f"   • Bias correction features removed: {len(bias_analysis.get('bias_correction_features', []))}")
            print(f"   • Context features removed: {len(bias_analysis.get('context_features', []))}")
            print(f"   • Safe features retained: {bias_analysis.get('safe_features_count', 0)}")

        # ML Results Summary
        if ml_results:
            print(f"\n🤖 BIAS CORRECTED FATIGUE PREDICTION & RFE ML MODELS:")
            if 'classification_summary' in ml_results:
                dist = ml_results['classification_summary']['fatigue_percentages']
                print(f"   • Corrected Fatigue Risk Distribution:")
                print(f"     - High Risk: {dist.get('high_risk', 0):.1f}%")
                print(f"     - Medium Risk: {dist.get('medium_risk', 0):.1f}%")
                print(f"     - Low Risk: {dist.get('low_risk', 0):.1f}%")

            # Feature filtering status
            if ml_results.get('data_leakage_prevented'):
                print(f"   • 🛡️ Data Leakage Prevention: ✅ ENABLED")
                print(f"   • Safe Dataset: {ml_results.get('safe_dataset_path', 'N/A')}")
            else:
                print(f"   • 🛡️ Data Leakage Prevention: ❌ DISABLED")

            # Bias correction status
            if ml_results.get('bias_correction_applied'):
                print(f"   • 🔧 Bias Correction: ✅ APPLIED")
            else:
                print(f"   • 🔧 Bias Correction: ❌ NOT APPLIED")

            if ml_results.get('best_accuracy'):
                print(f"   • Best RFE Model: {ml_results['best_model']}")
                print(f"   • Best Algorithm: {ml_results.get('best_algorithm', 'Unknown')}")
                print(f"   • Optimal Features: {ml_results.get('optimal_features_count', 0)}")
                print(f"   • Best Accuracy: {ml_results['best_accuracy']:.4f} ({ml_results['best_accuracy']*100:.2f}%)")

                if 'advanced_result' in ml_results:
                    adv = ml_results['advanced_result']
                    print(f"   • Advanced Model: {adv['accuracy_mean']:.4f} ({adv['accuracy_mean']*100:.2f}%)")
                    if 'f1_mean' in adv:
                        print(f"   • F1-Score: {adv['f1_mean']:.4f}")

        # Files Generated
        print(f"\n📁 FILES GENERATED:")
        print(f"   • dataset/processed/fatigue_classified.csv")
        if ml_results and ml_results.get('safe_dataset_path'):
            print(f"   • {ml_results['safe_dataset_path']} (safe for ML)")
        if ml_results and ml_results.get('best_accuracy'):
            print(f"   • results/rfe_ablation_study/rfe_results_*.csv (RFE analysis)")
            print(f"   • results/rfe_ablation_study/rfe_report_*.txt (RFE report)")
            print(f"   • results/rfe_ablation_study/optimal_features_*.py (optimal features)")
            print(f"   • results/clean_production_model/*.pkl")
            if ml_results.get('data_leakage_prevented'):
                print(f"   • 🛡️ Models trained with RFE-selected bias correction safe features")

        # Visualizations Generated
        print(f"\n📊 VISUALIZATIONS GENERATED:")
        print(f"   • results/visualizations/data_overview.png")
        print(f"   • results/visualizations/correlation_matrix.png")
        print(f"   • results/visualizations/time_series_analysis.png")
        print(f"   • results/visualizations/user_analysis.png")
        print(f"   • results/visualizations/gamification_analysis.png")
        print(f"   • results/visualizations/classification_analysis.png")
        print(f"   • results/visualizations/feature_analysis.png")
        print(f"   • results/visualizations/feature_filtering_analysis.png")
        print(f"   • results/visualizations/bias_correction_impact.png")
        print(f"   • results/visualizations/research_specific_analysis.png")
        if ml_results and ml_results.get('best_accuracy'):
            print(f"   • results/visualizations/rfe_analysis_*.png")
            print(f"   • results/visualizations/feature_selection_process_*.png")

        print(f"\n✅ Bias corrected fatigue classification with RFE and visualizations completed successfully!")
        print("="*80)

    def run_complete_pipeline(self):
        """Execute complete analysis pipeline with bias correction and RFE"""
        logger.info("🚀 STARTING COMPLETE BIAS CORRECTED ANALYSIS PIPELINE WITH RFE")
        logger.info("Includes: Data Processing + Bias Corrected Fatigue Prediction + RFE ML Models")
        logger.info("="*80)

        try:
            # Phase 1: Data Processing
            processed_data = self.run_data_processing()

            # Phase 2: Bias Corrected Fatigue Prediction with RFE (if enabled)
            ml_results = None
            if self.include_ml:
                ml_results = self.run_bias_corrected_prediction(processed_data)

            # Phase 3: Create Additional Research-Specific Visualizations
            logger.info("Phase 3: Creating additional research-specific visualizations...")
            self._create_research_specific_visualizations(processed_data)

            # Final Summary
            self._print_final_summary(processed_data, ml_results)

            logger.info("="*80)
            logger.info("🎉 COMPLETE BIAS CORRECTED PIPELINE WITH RFE FINISHED SUCCESSFULLY!")
            logger.info("="*80)

            return {
                'ml_results': ml_results,
                'processed_data': processed_data
            }

        except Exception as e:
            logger.error(f"Pipeline failed: {str(e)}")
            raise

    def _print_final_summary(self, data, ml_results):
        """Print comprehensive final summary with bias correction and RFE"""

        print("\n" + "="*80)
        print("🎉 COMPLETE BIAS CORRECTED ANALYSIS PIPELINE WITH RFE SUMMARY")
        print("="*80)

        # Data Processing Summary
        print(f"\n📊 DATA PROCESSING:")
        print(f"   • {len(data)} weekly observations processed")
        print(f"   • {data['identity'].nunique() if 'identity' in data.columns else 'N/A'} unique participants")

        # Bias Correction Summary
        if ml_results and 'bias_correction_analysis' in ml_results:
            bias_analysis = ml_results['bias_correction_analysis']
            print(f"\n🔧 BIAS CORRECTION ANALYSIS:")
            print(f"   • Bias correction features removed: {len(bias_analysis.get('bias_correction_features', []))}")
            print(f"   • Context features removed: {len(bias_analysis.get('context_features', []))}")
            print(f"   • Safe features retained: {bias_analysis.get('safe_features_count', 0)}")

        # ML Results Summary
        if ml_results:
            print(f"\n🤖 BIAS CORRECTED FATIGUE PREDICTION & RFE ML MODELS:")
            if 'classification_summary' in ml_results:
                dist = ml_results['classification_summary']['fatigue_percentages']
                print(f"   • Corrected Fatigue Risk Distribution:")
                print(f"     - High Risk: {dist.get('high_risk', 0):.1f}%")
                print(f"     - Medium Risk: {dist.get('medium_risk', 0):.1f}%")
                print(f"     - Low Risk: {dist.get('low_risk', 0):.1f}%")

            # Feature filtering status
            if ml_results.get('data_leakage_prevented'):
                print(f"   • 🛡️ Data Leakage Prevention: ✅ ENABLED")
                print(f"   • Safe Dataset: {ml_results.get('safe_dataset_path', 'N/A')}")
            else:
                print(f"   • 🛡️ Data Leakage Prevention: ❌ DISABLED")

            # Bias correction status
            if ml_results.get('bias_correction_applied'):
                print(f"   • 🔧 Bias Correction: ✅ APPLIED")
            else:
                print(f"   • 🔧 Bias Correction: ❌ NOT APPLIED")

            if ml_results.get('best_accuracy'):
                print(f"   • Best RFE Model: {ml_results['best_model']}")
                print(f"   • Best Algorithm: {ml_results.get('best_algorithm', 'Unknown')}")
                print(f"   • Optimal Features: {ml_results.get('optimal_features_count', 0)}")
                print(f"   • Best Accuracy: {ml_results['best_accuracy']:.4f} ({ml_results['best_accuracy']*100:.2f}%)")

                if 'advanced_result' in ml_results:
                    adv = ml_results['advanced_result']
                    print(f"   • Advanced Model: {adv['accuracy_mean']:.4f} ({adv['accuracy_mean']*100:.2f}%)")
                    if 'f1_mean' in adv:
                        print(f"   • F1-Score: {adv['f1_mean']:.4f}")

                # Model safety note
                if ml_results.get('data_leakage_prevented'):
                    print(f"   • ✅ Model trained with RFE-selected bias correction safe features (no data leakage)")
                else:
                    print(f"   • ⚠️ Model may have data leakage risk")

        # Files Generated
        print(f"\n📁 FILES GENERATED:")
        print(f"   • dataset/processed/weekly_merged_dataset_with_gamification.csv")
        if ml_results:
            print(f"   • dataset/processed/fatigue_classified.csv")
            if ml_results.get('safe_dataset_path'):
                print(f"   • {ml_results['safe_dataset_path']} (safe for ML)")
            if ml_results.get('best_accuracy'):
                print(f"   • results/rfe_ablation_study/rfe_results_*.csv (RFE analysis)")
                print(f"   • results/rfe_ablation_study/rfe_report_*.txt (RFE report)")
                print(f"   • results/rfe_ablation_study/optimal_features_*.py (optimal features)")
                print(f"   • results/clean_production_model/*.pkl")
                if ml_results.get('data_leakage_prevented'):
                    print(f"   • 🛡️ Models trained with RFE-selected bias correction safe features")

        print(f"\n✅ All bias corrected analyses with RFE completed successfully!")
        print("="*80)


def main():
    """Main function with argument parsing"""
    parser = argparse.ArgumentParser(description='Complete Bias Corrected Analysis Pipeline with RFE Feature Selection')
    parser.add_argument('--no-ml', action='store_true',
                       help='Skip machine learning components (data processing only)')
    parser.add_argument('--ml-only', action='store_true',
                       help='Run only RFE machine learning pipeline')
    parser.add_argument('--bias-corrected-only', action='store_true',
                       help='Run only bias corrected classification with RFE feature selection')
    parser.add_argument('--complete', action='store_true',
                       help='Run complete pipeline with bias correction and RFE (default behavior)')

    args = parser.parse_args()

    try:
        # Check if raw data exists (unless ml-only)
        if not args.ml_only:
            raw_data_path = Path("dataset/raw")
            if not raw_data_path.exists():
                print("❌ Raw data directory not found!")
                print("Please ensure dataset/raw/ contains strava.csv and pomokit.csv")
                return

            strava_file = raw_data_path / "strava.csv"
            pomokit_file = raw_data_path / "pomokit.csv"

            if not strava_file.exists() or not pomokit_file.exists():
                print("❌ Required data files not found!")
                print(f"Expected files:")
                print(f"  - {strava_file}")
                print(f"  - {pomokit_file}")
                return

        # Run appropriate pipeline
        if args.ml_only:
            # Create safe dataset first
            print("🛡️  Creating safe bias corrected ML dataset with RFE analysis...")
            safe_ml_path = "dataset/processed/safe_ml_bias_corrected_dataset.csv"

            # Check if safe dataset exists, if not create it
            if not Path(safe_ml_path).exists():
                from src.bias_corrected_title_classifier import BiasCorrectedTitleClassifier
                from src.feature_filter2 import FeatureFilter2

                # Need to run bias corrected classification first
                bias_classifier = BiasCorrectedTitleClassifier()
                classified_data = bias_classifier.process_bias_corrected_classification(
                    'dataset/processed/weekly_merged_dataset_with_gamification.csv'
                )
                classified_output = "dataset/processed/fatigue_classified.csv"
                classified_data.to_csv(classified_output, index=False)

                # Then create safe dataset
                feature_filter = FeatureFilter2()
                feature_filter.create_safe_dataset_for_bias_corrected_classifier(
                    classified_output,
                    safe_ml_path,
                    target_column='corrected_fatigue_risk'
                )

            # Run RFE ML pipeline with safe dataset
            print("🔍 Running RFE Analysis with multiple algorithms on bias corrected data...")
            rfe_study = RFEAblationStudy(
                data_path=safe_ml_path,
                target_column='corrected_fatigue_risk',
                random_state=42
            )
            rfe_study.load_data()
            study_results = rfe_study.run_complete_rfe_study()

            # Save results
            results_file, report_file = rfe_study.save_results(study_results)

            print(f"\n🎉 Bias Corrected RFE ML Pipeline Completed!")
            print(f"📋 Results: {results_file}")
            print(f"📋 Report: {report_file}")

            # Show best results
            if 'analysis' in study_results and 'best_overall' in study_results['analysis']:
                best = study_results['analysis']['best_overall']
                print(f"\n🏆 Best Configuration:")
                print(f"   • Algorithm: {best.get('algorithm_name', 'Unknown')}")
                print(f"   • Features: {best.get('n_features', 0)}")
                print(f"   • Accuracy: {best.get('accuracy', 0.0):.4f} ({best.get('accuracy', 0.0)*100:.2f}%)")

        elif args.bias_corrected_only:
            # Run only bias corrected classification with RFE feature selection
            print("🚀 Running Bias Corrected Classification Pipeline with RFE Feature Selection...")
            pipeline = BiasCorrectedRFEAnalysisPipeline(include_ml=True)
            results = pipeline.run_bias_corrected_only_pipeline()
            print(f"\n🎉 Bias Corrected Classification Pipeline with RFE Completed!")
        else:
            # Run complete pipeline with bias correction and RFE (default)
            include_ml = not args.no_ml
            pipeline = BiasCorrectedRFEAnalysisPipeline(include_ml=include_ml)
            pipeline.run_complete_pipeline()

    except KeyboardInterrupt:
        print("\n❌ Pipeline interrupted by user")
    except Exception as e:
        print(f"\n❌ Pipeline failed: {str(e)}")
        logger.error(f"Pipeline failed: {str(e)}", exc_info=True)


if __name__ == "__main__":
    main()
