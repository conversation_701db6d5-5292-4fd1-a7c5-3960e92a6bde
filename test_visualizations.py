#!/usr/bin/env python3
"""
Test script to verify all visualizations are saved to results/visualizations/
"""

import sys
import os
from pathlib import Path
sys.path.append('src')

def test_data_processor():
    """Test data processor visualizations"""
    print("🔄 Testing Data Processor Visualizations...")
    
    try:
        from data_processor import DataProcessor
        import pandas as pd
        
        processor = DataProcessor()
        
        # Load existing data if available
        processed_file = processor.processed_data_path / "weekly_merged_dataset_with_gamification.csv"
        if processed_file.exists():
            data = pd.read_csv(processed_file)
            print(f"✅ Loaded data: {data.shape}")
            
            # Test individual visualization
            print("📊 Creating data overview...")
            processor.visualize_data_overview(data, save_plots=True)
            
            print("📊 Creating correlation matrix...")
            processor.visualize_correlation_matrix(data, save_plots=True)
            
            print("✅ Data processor test completed!")
        else:
            print("❌ No processed data found")
            
    except Exception as e:
        print(f"❌ Error in data processor test: {e}")

def test_classification():
    """Test classification visualizations"""
    print("\n🧠 Testing Classification Visualizations...")
    
    try:
        from bias_corrected_title_classifier import BiasCorrectedTitleClassifier
        import pandas as pd
        
        classifier = BiasCorrectedTitleClassifier()
        
        # Check if classification results exist
        classified_file = Path("dataset/processed/fatigue_classified.csv")
        if classified_file.exists():
            data = pd.read_csv(classified_file)
            print(f"✅ Loaded classification data: {data.shape}")
            
            # Test individual visualization
            print("📊 Creating classification analysis...")
            classifier.visualize_classification_results(data, save_plots=True)
            
            print("✅ Classification test completed!")
        else:
            print("❌ No classification data found")
            
    except Exception as e:
        print(f"❌ Error in classification test: {e}")

def test_feature_filtering():
    """Test feature filtering visualizations"""
    print("\n🔍 Testing Feature Filtering Visualizations...")
    
    try:
        from feature_filter2 import FeatureFilter2
        import pandas as pd
        
        filter_obj = FeatureFilter2()
        
        # Check if input file exists
        input_file = Path("dataset/processed/fatigue_classified.csv")
        if input_file.exists():
            print("✅ Input file found")
            
            # Load data and test filtering
            original_df = pd.read_csv(input_file)
            filtered_df, safe_features, removed_info = filter_obj.filter_features_for_ml(
                original_df, 'corrected_fatigue_risk'
            )
            
            print("📊 Creating filtering analysis...")
            filter_obj.visualize_feature_filtering_results(
                original_df, filtered_df, removed_info, save_plots=True
            )
            
            print("📊 Creating bias correction impact...")
            filter_obj.visualize_bias_correction_impact(original_df, save_plots=True)
            
            print("✅ Feature filtering test completed!")
        else:
            print("❌ No input file found for filtering")
            
    except Exception as e:
        print(f"❌ Error in feature filtering test: {e}")

def check_results():
    """Check if all files are in results/visualizations/"""
    print("\n📁 Checking Results Directory...")
    
    viz_dir = Path("results/visualizations")
    if viz_dir.exists():
        print(f"✅ Directory exists: {viz_dir}")
        
        files = list(viz_dir.glob("*.png"))
        print(f"📊 Found {len(files)} visualization files:")
        
        for file in sorted(files):
            size_mb = file.stat().st_size / (1024 * 1024)
            print(f"  ✅ {file.name} ({size_mb:.2f} MB)")
            
        return len(files)
    else:
        print("❌ Results/visualizations directory not found")
        return 0

def main():
    """Main test function"""
    print("🧪 VISUALIZATION LOCATION TEST")
    print("=" * 60)
    print("Testing that all visualizations are saved to results/visualizations/")
    print("=" * 60)
    
    # Run tests
    test_data_processor()
    test_classification()
    test_feature_filtering()
    
    # Check results
    file_count = check_results()
    
    print("\n" + "=" * 60)
    print("🎉 TEST COMPLETED!")
    print("=" * 60)
    print(f"📊 Total visualization files: {file_count}")
    print("📁 All files should be in: results/visualizations/")
    
    if file_count >= 5:
        print("✅ SUCCESS: Visualizations are being saved to the correct location!")
    else:
        print("⚠️  WARNING: Some visualizations may not have been created")

if __name__ == "__main__":
    main()
