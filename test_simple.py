#!/usr/bin/env python3
"""
Simple test to check if visualizations are being created
"""

from pathlib import Path
import sys
sys.path.append('src')

def check_visualizations():
    """Check current visualizations"""
    viz_dir = Path("results/visualizations")
    
    if not viz_dir.exists():
        print("❌ Directory does not exist")
        return
    
    files = list(viz_dir.glob("*.png"))
    print(f"📊 Found {len(files)} visualization files:")
    
    for file in sorted(files):
        size_mb = file.stat().st_size / (1024 * 1024)
        print(f"  ✅ {file.name} ({size_mb:.2f} MB)")

def test_data_processor():
    """Test data processor visualization"""
    try:
        from data_processor import DataProcessor
        import pandas as pd
        
        print("🔄 Testing DataProcessor...")
        
        # Check if processed data exists
        data_path = "dataset/processed/weekly_merged_dataset_with_gamification.csv"
        if Path(data_path).exists():
            print(f"✅ Found processed data: {data_path}")
            
            processor = DataProcessor()
            data = pd.read_csv(data_path)
            
            print(f"📊 Data shape: {data.shape}")
            print("🎨 Creating visualizations...")
            
            processor.create_comprehensive_report(data, save_plots=True)
            print("✅ DataProcessor visualizations completed")
            
        else:
            print(f"❌ Processed data not found: {data_path}")
            print("🔄 Running full data processing...")
            
            processor = DataProcessor()
            data = processor.process_all()
            processor.create_comprehensive_report(data, save_plots=True)
            print("✅ Full data processing and visualizations completed")
            
    except Exception as e:
        print(f"❌ Error in DataProcessor test: {e}")

if __name__ == "__main__":
    print("🧪 SIMPLE VISUALIZATION TEST")
    print("=" * 50)
    
    print("\n1. Checking existing visualizations:")
    check_visualizations()
    
    print("\n2. Testing DataProcessor:")
    test_data_processor()
    
    print("\n3. Final check:")
    check_visualizations()
    
    print("\n✅ Test completed!")
