#!/usr/bin/env python3
"""
Verify that main5.py modifications are working by checking the results
"""

from pathlib import Path
import os
from datetime import datetime

def check_visualization_files():
    """Check all visualization files and their timestamps"""
    print("📊 CHECKING VISUALIZATION FILES")
    print("=" * 60)
    
    viz_dir = Path("results/visualizations")
    
    if not viz_dir.exists():
        print("❌ Directory does not exist: results/visualizations/")
        return []
    
    files = list(viz_dir.glob("*.png"))
    
    print(f"📁 Directory: {viz_dir.absolute()}")
    print(f"📊 Found {len(files)} visualization files:")
    print("-" * 60)
    
    file_info = []
    total_size = 0
    
    for i, file in enumerate(sorted(files), 1):
        stat = file.stat()
        size_bytes = stat.st_size
        size_mb = size_bytes / (1024 * 1024)
        total_size += size_mb
        
        # Get modification time
        mod_time = datetime.fromtimestamp(stat.st_mtime)
        
        print(f"{i:2d}. ✅ {file.name}")
        print(f"     Size: {size_mb:.2f} MB ({size_bytes:,} bytes)")
        print(f"     Modified: {mod_time.strftime('%Y-%m-%d %H:%M:%S')}")
        print()
        
        file_info.append({
            'name': file.name,
            'size_mb': size_mb,
            'modified': mod_time
        })
    
    print("-" * 60)
    print(f"📊 SUMMARY:")
    print(f"   Total files: {len(files)}")
    print(f"   Total size: {total_size:.2f} MB")
    print(f"   Average size: {total_size/len(files):.2f} MB per file" if files else "   No files found")
    
    return file_info

def analyze_main5_expected_output():
    """Analyze what main5.py should produce"""
    print("\n🎯 MAIN5.PY EXPECTED OUTPUT ANALYSIS")
    print("=" * 60)
    
    expected_files = [
        ('data_overview.png', 'Data processing overview (from DataProcessor)'),
        ('correlation_matrix.png', 'Correlation analysis (from DataProcessor)'),
        ('time_series_analysis.png', 'Time series trends (from DataProcessor)'),
        ('user_analysis.png', 'User performance analysis (from DataProcessor)'),
        ('gamification_analysis.png', 'Gamification metrics (from DataProcessor)'),
        ('classification_analysis.png', 'Classification results (from BiasCorrectedTitleClassifier)'),
        ('feature_analysis.png', 'Feature importance (from BiasCorrectedTitleClassifier)'),
        ('feature_filtering_analysis.png', 'Feature filtering results (from FeatureFilter2)'),
        ('bias_correction_impact.png', 'Bias correction impact (from FeatureFilter2)'),
        ('research_specific_analysis.png', 'Research-specific analysis (NEW from main5.py)'),
    ]
    
    viz_dir = Path("results/visualizations")
    
    print("📋 Expected files from main5.py modifications:")
    print("-" * 60)
    
    found_count = 0
    missing_files = []
    
    for filename, description in expected_files:
        file_path = viz_dir / filename
        if file_path.exists():
            size_mb = file_path.stat().st_size / (1024 * 1024)
            mod_time = datetime.fromtimestamp(file_path.stat().st_mtime)
            print(f"  ✅ {filename} ({size_mb:.2f} MB)")
            print(f"      {description}")
            print(f"      Modified: {mod_time.strftime('%Y-%m-%d %H:%M:%S')}")
            found_count += 1
        else:
            print(f"  ❌ {filename}")
            print(f"      {description}")
            missing_files.append(filename)
        print()
    
    print("-" * 60)
    print(f"📊 ANALYSIS RESULTS:")
    print(f"   Expected files: {len(expected_files)}")
    print(f"   Found files: {found_count}")
    print(f"   Missing files: {len(missing_files)}")
    
    if missing_files:
        print(f"\n❌ Missing files:")
        for file in missing_files:
            print(f"   - {file}")
    
    success_rate = (found_count / len(expected_files)) * 100
    print(f"\n🎯 Success Rate: {success_rate:.1f}%")
    
    return success_rate >= 80  # Consider 80% or higher as success

def check_main5_modifications():
    """Check if main5.py has the expected modifications"""
    print("\n🔧 CHECKING MAIN5.PY MODIFICATIONS")
    print("=" * 60)
    
    main5_path = Path("main5.py")
    
    if not main5_path.exists():
        print("❌ main5.py not found")
        return False
    
    with open(main5_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Check for key modifications
    modifications = [
        ('create_comprehensive_report', 'DataProcessor visualization calls'),
        ('create_comprehensive_classification_report', 'Classification visualization calls'),
        ('create_comprehensive_filtering_report', 'Feature filtering visualization calls'),
        ('create_comprehensive_rfe_report', 'RFE visualization calls'),
        ('_create_research_specific_visualizations', 'Research-specific visualization method'),
        ('results/visualizations', 'Correct output directory references'),
    ]
    
    found_modifications = 0
    
    for search_term, description in modifications:
        if search_term in content:
            print(f"  ✅ {description}")
            found_modifications += 1
        else:
            print(f"  ❌ {description}")
    
    print(f"\n📊 Modifications found: {found_modifications}/{len(modifications)}")
    
    return found_modifications >= len(modifications) - 1  # Allow 1 missing

def main():
    """Main verification function"""
    print("🧪 MAIN5.PY VISUALIZATION VERIFICATION")
    print("=" * 80)
    print("Verifying that main5.py modifications are working correctly")
    print("=" * 80)
    
    # Check current visualization files
    file_info = check_visualization_files()
    
    # Analyze expected output
    output_success = analyze_main5_expected_output()
    
    # Check modifications
    mod_success = check_main5_modifications()
    
    # Final verdict
    print("\n" + "=" * 80)
    print("🎯 FINAL VERIFICATION RESULTS")
    print("=" * 80)
    
    if file_info:
        print(f"✅ Visualization files exist: {len(file_info)} files")
    else:
        print("❌ No visualization files found")
    
    if output_success:
        print("✅ Expected output files: FOUND (80%+ success rate)")
    else:
        print("⚠️  Expected output files: INCOMPLETE")
    
    if mod_success:
        print("✅ main5.py modifications: PRESENT")
    else:
        print("❌ main5.py modifications: MISSING")
    
    # Overall assessment
    overall_success = bool(file_info) and output_success and mod_success
    
    if overall_success:
        print(f"\n🎉 OVERALL ASSESSMENT: SUCCESS")
        print("✅ main5.py has been successfully modified to create visualizations")
        print("✅ Visualizations are being saved to results/visualizations/")
        print("✅ Expected files are present")
        
        print(f"\n📊 READY TO USE:")
        print(f"   Run: python main5.py --bias-corrected-only")
        print(f"   Expected: 10+ visualization files in results/visualizations/")
        
    else:
        print(f"\n⚠️  OVERALL ASSESSMENT: NEEDS ATTENTION")
        if not file_info:
            print("❌ No visualization files found - may need to run main5.py")
        if not output_success:
            print("❌ Some expected files missing - check main5.py execution")
        if not mod_success:
            print("❌ main5.py modifications incomplete - check code changes")
    
    print("\n✅ Verification completed!")

if __name__ == "__main__":
    main()
