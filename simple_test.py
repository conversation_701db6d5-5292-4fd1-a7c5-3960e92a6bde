#!/usr/bin/env python3
"""
Simple test to create feature filtering visualizations
"""

import sys
sys.path.append('src')

def test_feature_filtering():
    """Test feature filtering visualizations"""
    print("Testing feature filtering...")
    
    try:
        from feature_filter2 import FeatureFilter2
        import pandas as pd
        from pathlib import Path
        
        # Check if input file exists
        input_file = "dataset/processed/fatigue_classified.csv"
        if not Path(input_file).exists():
            print(f"Input file not found: {input_file}")
            return
        
        print(f"Loading data from: {input_file}")
        original_df = pd.read_csv(input_file)
        print(f"Data shape: {original_df.shape}")
        
        filter_obj = FeatureFilter2()
        
        # Filter features
        filtered_df, safe_features, removed_info = filter_obj.filter_features_for_ml(
            original_df, 'corrected_fatigue_risk'
        )
        
        print(f"Filtered shape: {filtered_df.shape}")
        
        # Create visualizations
        print("Creating feature filtering visualization...")
        filter_obj.visualize_feature_filtering_results(
            original_df, filtered_df, removed_info, save_plots=True
        )
        
        print("Creating bias correction impact visualization...")
        filter_obj.visualize_bias_correction_impact(original_df, save_plots=True)
        
        print("✅ Feature filtering visualizations completed!")
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_feature_filtering()
