#!/usr/bin/env python3
"""
Example script showing how to use the visualization features in data_processor.py

This script demonstrates various ways to create visualizations directly from
the DataProcessor class without needing separate visualization files.
"""

import sys
import os
sys.path.append('src')

from data_processor import DataProcessor
from constants import DataConstants
import pandas as pd

def example_basic_usage():
    """Basic example: Process data and create all visualizations"""
    print("="*60)
    print("BASIC USAGE EXAMPLE")
    print("="*60)
    
    # Initialize processor
    processor = DataProcessor()
    
    # Process data and create comprehensive report
    data = processor.process_all()
    processor.create_comprehensive_report(data, save_plots=True)
    
    print("✓ Basic usage completed - all visualizations saved to results/visualizations/")

def example_selective_visualizations():
    """Example: Create only specific visualizations"""
    print("\n" + "="*60)
    print("SELECTIVE VISUALIZATIONS EXAMPLE")
    print("="*60)
    
    processor = DataProcessor()
    
    # Load existing processed data to avoid reprocessing
    processed_file = processor.processed_data_path / DataConstants.OUTPUT_FILENAME
    if processed_file.exists():
        data = pd.read_csv(processed_file)
        print(f"✓ Loaded existing data: {data.shape}")
    else:
        data = processor.process_all()
        print(f"✓ Processed new data: {data.shape}")
    
    # Create only specific visualizations
    print("\n1. Creating data overview...")
    processor.visualize_data_overview(data, save_plots=True)
    
    print("2. Creating correlation matrix...")
    processor.visualize_correlation_matrix(data, save_plots=True)
    
    print("3. Creating gamification analysis...")
    processor.visualize_gamification_analysis(data, save_plots=True)
    
    print("✓ Selective visualizations completed")

def example_analysis_only():
    """Example: Generate reports without saving plots"""
    print("\n" + "="*60)
    print("ANALYSIS ONLY EXAMPLE")
    print("="*60)
    
    processor = DataProcessor()
    
    # Load data
    processed_file = processor.processed_data_path / DataConstants.OUTPUT_FILENAME
    if processed_file.exists():
        data = pd.read_csv(processed_file)
    else:
        data = processor.process_all()
    
    # Generate data quality report only
    print("Generating data quality report...")
    processor.generate_data_quality_report(data)
    
    # Create visualizations without saving (for quick analysis)
    print("\nCreating quick visualizations (not saved)...")
    processor.visualize_user_analysis(data, save_plots=False)
    
    print("✓ Analysis completed")

def example_custom_workflow():
    """Example: Custom workflow with data processing and specific visualizations"""
    print("\n" + "="*60)
    print("CUSTOM WORKFLOW EXAMPLE")
    print("="*60)
    
    processor = DataProcessor()
    
    # Step 1: Load and clean data
    print("Step 1: Loading and cleaning data...")
    strava_data, pomokit_data = processor.load_raw_data()
    strava_clean = processor.clean_strava_data(strava_data)
    pomokit_clean = processor.clean_pomokit_data(pomokit_data)
    
    # Step 2: Create weekly aggregation
    print("Step 2: Creating weekly aggregation...")
    weekly_data = processor.create_weekly_aggregation(strava_clean, pomokit_clean)
    
    # Step 3: Add features
    print("Step 3: Adding features...")
    weekly_data = processor.add_title_features(weekly_data)
    final_data = processor.add_gamification_variables(weekly_data)
    final_data = processor.generate_user_identities(final_data)
    final_data = processor.finalize_dataset(final_data)
    
    # Step 4: Create specific visualizations
    print("Step 4: Creating visualizations...")
    processor.visualize_time_series(final_data, save_plots=True)
    processor.visualize_gamification_analysis(final_data, save_plots=True)
    
    # Step 5: Generate summary
    print("Step 5: Generating summary...")
    processor.generate_data_quality_report(final_data)
    
    print("✓ Custom workflow completed")

def main():
    """Main function to run all examples"""
    print("CARDIOVASCULAR ACTIVITY VISUALIZATION EXAMPLES")
    print("=" * 80)
    
    # Run examples
    try:
        example_basic_usage()
        example_selective_visualizations()
        example_analysis_only()
        example_custom_workflow()
        
        print("\n" + "="*80)
        print("ALL EXAMPLES COMPLETED SUCCESSFULLY!")
        print("="*80)
        print("Check the 'results/visualizations/' directory for generated visualization files:")
        print("- data_overview.png")
        print("- correlation_matrix.png")
        print("- time_series_analysis.png")
        print("- user_analysis.png")
        print("- gamification_analysis.png")
        print("- classification_analysis.png")
        print("- feature_analysis.png")
        print("- feature_filtering_analysis.png")
        print("- bias_correction_impact.png")
        
    except Exception as e:
        print(f"Error running examples: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
