# Optimal Feature Set from RFE Analysis
# Generated on 2025-07-21 01:06:49
# Best Algorithm: XGBoost
# Accuracy: 0.9554 ± 0.0318

OPTIMAL_FEATURES = [
    'activity_days',
    'consistency_score',
    'pomokit_title_count',
    'productivity_points',
    'total_cycles',
]

# Usage example:
# X_optimal = X[OPTIMAL_FEATURES]

# Comprehensive Feature Importance Analysis
BASELINE_ACCURACY = 0.9520

# Feature Impact When Removed (Ablation-Style)
FEATURE_IMPACT = {
    'activity_days': -0.0068,  # -0.72% impact
    'pomokit_title_count': 0.0000,  # 0.00% impact
    'productivity_points': 0.0000,  # 0.00% impact
    'total_cycles': 0.0000,  # 0.00% impact
    'consistency_score': 0.0000,  # 0.00% impact
}

# Feature Importance (Permutation Importance)
FEATURE_IMPORTANCE = {
    'activity_days': 2.81,  # 2.81%
    'pomokit_title_count': 33.74,  # 33.74%
    'productivity_points': 0.50,  # 0.50%
    'total_cycles': 0.00,  # 0.00%
    'consistency_score': 62.96,  # 62.96%
}

# Sorted by impact when removed (descending)
FEATURES_BY_IMPACT = [
    'activity_days',  # -0.72% impact when removed
    'pomokit_title_count',  # 0.00% impact when removed
    'productivity_points',  # 0.00% impact when removed
    'total_cycles',  # 0.00% impact when removed
    'consistency_score',  # 0.00% impact when removed
]

# Sorted by permutation importance (descending)
FEATURES_BY_IMPORTANCE = [
    'activity_days',  # 2.81% permutation importance
    'pomokit_title_count',  # 33.74% permutation importance
    'productivity_points',  # 0.50% permutation importance
    'total_cycles',  # 0.00% permutation importance
    'consistency_score',  # 62.96% permutation importance
]

# Feature Interpretations
FEATURE_INTERPRETATIONS = {
    'activity_days': 'MINIMAL - Negligible performance impact',
    'pomokit_title_count': 'MINIMAL - Negligible performance impact',
    'productivity_points': 'MINIMAL - Negligible performance impact',
    'total_cycles': 'MINIMAL - Negligible performance impact',
    'consistency_score': 'MINIMAL - Negligible performance impact',
}
