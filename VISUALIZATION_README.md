# Cardiovascular Activity & Productivity Data Visualization

## Overview

This project now includes comprehensive visualization capabilities built directly into the `DataProcessor` class in `src/data_processor.py`. You can create various types of visualizations and analysis reports without needing separate visualization files.

## Features

### 🎯 Built-in Visualizations

-   **Data Overview**: Distribution plots and scatter plots of key metrics
-   **Correlation Matrix**: Heatmap showing relationships between numeric variables
-   **Time Series Analysis**: Trends of key metrics over time
-   **User Analysis**: Top performers and user-specific insights
-   **Gamification Analysis**: Achievement rates, points distribution, and balance analysis
-   **Data Quality Report**: Comprehensive data quality assessment

### 📊 Generated Visualizations

All visualizations are automatically saved as high-quality PNG files (300 DPI) in the `results/visualizations/` directory:

1. `data_overview.png` - Overview of key metrics distributions
2. `correlation_matrix.png` - Correlation heatmap
3. `time_series_analysis.png` - Time series trends
4. `user_analysis.png` - User performance analysis
5. `gamification_analysis.png` - Gamification metrics analysis
6. `classification_analysis.png` - Classification results analysis
7. `feature_analysis.png` - Feature importance analysis
8. `feature_filtering_analysis.png` - Feature filtering results
9. `bias_correction_impact.png` - Bias correction impact analysis

## Quick Start

### Method 1: Complete Processing + All Visualizations

```python
from src.data_processor import DataProcessor

# Initialize processor
processor = DataProcessor()

# Process data and create all visualizations
data = processor.process_all()
processor.create_comprehensive_report(data, save_plots=True)
```

### Method 2: Individual Visualizations

```python
from src.data_processor import DataProcessor
import pandas as pd

processor = DataProcessor()

# Load existing processed data
data = pd.read_csv('dataset/processed/weekly_merged_dataset_with_gamification.csv')

# Create specific visualizations
processor.visualize_data_overview(data, save_plots=True)
processor.visualize_correlation_matrix(data, save_plots=True)
processor.visualize_user_analysis(data, save_plots=True)
processor.visualize_gamification_analysis(data, save_plots=True)
processor.visualize_time_series(data, save_plots=True)

# Generate data quality report
processor.generate_data_quality_report(data)
```

### Method 3: Command Line Usage

```bash
# Run complete processing and visualization
python src/data_processor.py

# Run individual visualization examples
python src/data_processor.py individual

# Run comprehensive examples
python example_visualizations.py
```

## Available Methods

### Core Visualization Methods

#### `visualize_data_overview(data, save_plots=True)`

Creates a 2x3 subplot showing:

-   Distribution of weekly total distance
-   Distribution of weekly total cycles
-   Physical activity vs productivity scatter plot
-   Consistency score distribution
-   Achievement rate distribution
-   Weekly efficiency by top users

#### `visualize_correlation_matrix(data, save_plots=True)`

Creates a correlation heatmap of all numeric variables with:

-   Masked upper triangle for cleaner view
-   Color-coded correlation strength
-   Annotated correlation values

#### `visualize_time_series(data, save_plots=True)`

Creates time series plots showing trends over weeks:

-   Average weekly distance
-   Average weekly cycles
-   Average consistency score
-   Average achievement rate

#### `visualize_user_analysis(data, save_plots=True)`

Creates user-focused analysis:

-   Top 10 users by total distance
-   Top 10 users by total cycles
-   User consistency vs achievement scatter plot
-   Activity frequency distribution
-   Prints top performers summary

#### `visualize_gamification_analysis(data, save_plots=True)`

Creates gamification-specific visualizations:

-   Activity points vs productivity points scatter
-   Achievement rate quartile distribution
-   Gamification balance distribution
-   Weekly efficiency vs achievement rate

#### `generate_data_quality_report(data)`

Generates comprehensive text report including:

-   Dataset basic information
-   Missing values analysis
-   Data types breakdown
-   Outlier analysis for numeric columns
-   Unique values analysis for categorical columns

#### `create_comprehensive_report(data, save_plots=True)`

Runs all visualization methods plus generates summary statistics and data quality report.

## Example Workflows

### Workflow 1: Quick Analysis

```python
processor = DataProcessor()
data = processor.process_all()
processor.create_comprehensive_report(data)
```

### Workflow 2: Custom Analysis

```python
processor = DataProcessor()

# Load and process data step by step
strava_data, pomokit_data = processor.load_raw_data()
strava_clean = processor.clean_strava_data(strava_data)
pomokit_clean = processor.clean_pomokit_data(pomokit_data)
weekly_data = processor.create_weekly_aggregation(strava_clean, pomokit_clean)

# Add features
weekly_data = processor.add_title_features(weekly_data)
final_data = processor.add_gamification_variables(weekly_data)

# Create specific visualizations
processor.visualize_gamification_analysis(final_data)
processor.visualize_user_analysis(final_data)
```

### Workflow 3: Analysis Without Saving Plots

```python
processor = DataProcessor()
data = pd.read_csv('dataset/processed/weekly_merged_dataset_with_gamification.csv')

# Quick analysis without saving files
processor.visualize_data_overview(data, save_plots=False)
processor.generate_data_quality_report(data)
```

## Dependencies

The visualization features require these additional packages:

-   `matplotlib` - For creating plots
-   `seaborn` - For enhanced statistical visualizations
-   `pandas` - For data manipulation
-   `numpy` - For numerical operations

Install with:

```bash
pip install matplotlib seaborn pandas numpy
```

## Output Files

All visualization files are saved to `results/visualizations/` with the following naming convention:

-   High resolution (300 DPI) PNG format
-   Descriptive filenames
-   Timestamp information in logs for RFE analysis files

## Tips

1. **Performance**: Use `save_plots=False` for quick analysis without file I/O
2. **Memory**: Close matplotlib figures automatically (handled by the code)
3. **Customization**: Modify color schemes and styles in the import section
4. **Batch Processing**: Use the comprehensive report for complete analysis
5. **Individual Analysis**: Use specific methods for focused analysis

## Troubleshooting

### Common Issues:

1. **Import Error**: Make sure you're in the project root directory
2. **Missing Data**: Ensure raw data files exist in `dataset/raw/`
3. **Permission Error**: Check write permissions for `results/visualizations/`
4. **Memory Issues**: Use individual visualizations instead of comprehensive report

### Getting Help:

-   Check the console output for detailed logging information
-   Review the data quality report for data issues
-   Use `save_plots=False` to test visualizations quickly
