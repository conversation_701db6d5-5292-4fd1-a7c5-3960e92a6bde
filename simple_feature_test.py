"""
Simple Feature Importance Test
Menguji fitur-fitur SHAP yang sudah teridentifikasi dengan cara sederhana

Berdasarkan hasil SHAP yang sudah ada di:
- results/feature_selection/selected_features/consensus_features_*.txt
- results/feature_selection/shap_features/top_10_random_forest_*.txt
"""

import pandas as pd
import numpy as np
from sklearn.model_selection import cross_val_score, StratifiedKFold
from sklearn.ensemble import RandomForestClassifier
from sklearn.linear_model import LogisticRegression
from sklearn.preprocessing import StandardScaler
from sklearn.pipeline import Pipeline
import matplotlib.pyplot as plt
from pathlib import Path
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def load_consensus_features():
    """Load consensus features from file"""
    consensus_files = list(Path("results/feature_selection/selected_features/").glob("consensus_features_*.txt"))
    
    if not consensus_files:
        logger.error("No consensus features file found!")
        return []
    
    consensus_file = consensus_files[0]  # Use the most recent one
    logger.info(f"Loading consensus features from: {consensus_file}")
    
    features = []
    with open(consensus_file, 'r') as f:
        for line in f:
            if line.strip() and not line.startswith('#'):
                # Extract feature name (before the first space)
                feature_name = line.split()[0].strip()
                features.append(feature_name)
    
    logger.info(f"Found {len(features)} consensus features: {features}")
    return features

def load_top_rf_features():
    """Load top Random Forest features from file"""
    rf_files = list(Path("results/feature_selection/shap_features/").glob("top_10_random_forest_*.txt"))
    
    if not rf_files:
        logger.error("No Random Forest features file found!")
        return []
    
    rf_file = rf_files[0]  # Use the most recent one
    logger.info(f"Loading RF features from: {rf_file}")
    
    features = []
    with open(rf_file, 'r') as f:
        for line in f:
            if line.strip() and not line.startswith('#') and '.' in line:
                # Extract feature name (between number and parenthesis)
                parts = line.split('(')[0].strip()  # Remove SHAP value part
                if '.' in parts:
                    feature_name = parts.split('.', 1)[1].strip()  # Remove number
                    features.append(feature_name)
    
    logger.info(f"Found {len(features)} RF features: {features}")
    return features

def test_feature_sets(data_path, target_column='fatigue_risk'):
    """Test different feature sets"""
    
    # Load data
    logger.info(f"Loading data from: {data_path}")
    data = pd.read_csv(data_path)
    
    X = data.drop(columns=[target_column])
    y = data[target_column]
    
    logger.info(f"Dataset shape: {X.shape}")
    logger.info(f"Target distribution: {y.value_counts().to_dict()}")
    
    # Load feature sets
    consensus_features = load_consensus_features()
    rf_features = load_top_rf_features()
    
    # Filter features that exist in dataset
    consensus_features = [f for f in consensus_features if f in X.columns]
    rf_features = [f for f in rf_features if f in X.columns]
    
    logger.info(f"Consensus features in dataset: {len(consensus_features)}")
    logger.info(f"RF features in dataset: {len(rf_features)}")
    
    # Setup cross-validation
    cv = StratifiedKFold(n_splits=5, shuffle=True, random_state=42)
    
    # Test scenarios
    scenarios = {
        'All Features': list(X.columns),
        'Consensus Features': consensus_features,
        'Top 10 RF Features': rf_features,
        'Top 5 Consensus': consensus_features[:5],
        'Top 3 Consensus': consensus_features[:3],
    }
    
    # Add random feature sets for comparison
    if len(X.columns) >= 10:
        np.random.seed(42)
        scenarios['Random 10 Features'] = list(np.random.choice(X.columns, 10, replace=False))
    if len(X.columns) >= 5:
        np.random.seed(43)
        scenarios['Random 5 Features'] = list(np.random.choice(X.columns, 5, replace=False))
    
    results = {}
    
    # Test each scenario
    for scenario_name, features in scenarios.items():
        if not features:
            logger.warning(f"Skipping {scenario_name} - no features available")
            continue
            
        logger.info(f"\nTesting {scenario_name} ({len(features)} features)...")
        
        X_subset = X[features]
        
        # Random Forest
        rf = RandomForestClassifier(n_estimators=100, random_state=42)
        rf_scores = cross_val_score(rf, X_subset, y, cv=cv, scoring='accuracy')
        
        # Logistic Regression with scaling
        lr_pipeline = Pipeline([
            ('scaler', StandardScaler()),
            ('lr', LogisticRegression(random_state=42, max_iter=1000))
        ])
        lr_scores = cross_val_score(lr_pipeline, X_subset, y, cv=cv, scoring='accuracy')
        
        results[scenario_name] = {
            'n_features': len(features),
            'features': features,
            'rf_mean': rf_scores.mean(),
            'rf_std': rf_scores.std(),
            'lr_mean': lr_scores.mean(),
            'lr_std': lr_scores.std()
        }
        
        logger.info(f"  RF: {rf_scores.mean():.4f} ± {rf_scores.std():.4f}")
        logger.info(f"  LR: {lr_scores.mean():.4f} ± {lr_scores.std():.4f}")
    
    return results

def create_comparison_plot(results):
    """Create comparison plot of results"""
    
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(16, 6))
    
    scenarios = list(results.keys())
    rf_means = [results[s]['rf_mean'] for s in scenarios]
    rf_stds = [results[s]['rf_std'] for s in scenarios]
    lr_means = [results[s]['lr_mean'] for s in scenarios]
    lr_stds = [results[s]['lr_std'] for s in scenarios]
    n_features = [results[s]['n_features'] for s in scenarios]
    
    # Random Forest comparison
    bars1 = ax1.bar(range(len(scenarios)), rf_means, yerr=rf_stds, capsize=5, alpha=0.7)
    ax1.set_title('Random Forest: Feature Set Comparison')
    ax1.set_ylabel('Accuracy')
    ax1.set_xticks(range(len(scenarios)))
    ax1.set_xticklabels([f"{s}\n({n_features[i]} features)" for i, s in enumerate(scenarios)], 
                       rotation=45, ha='right')
    ax1.grid(True, alpha=0.3)
    
    # Add value labels on bars
    for i, (bar, mean, std) in enumerate(zip(bars1, rf_means, rf_stds)):
        ax1.text(bar.get_x() + bar.get_width()/2, bar.get_height() + std + 0.005,
                f'{mean:.3f}', ha='center', va='bottom', fontsize=9)
    
    # Logistic Regression comparison
    bars2 = ax2.bar(range(len(scenarios)), lr_means, yerr=lr_stds, capsize=5, alpha=0.7, color='orange')
    ax2.set_title('Logistic Regression: Feature Set Comparison')
    ax2.set_ylabel('Accuracy')
    ax2.set_xticks(range(len(scenarios)))
    ax2.set_xticklabels([f"{s}\n({n_features[i]} features)" for i, s in enumerate(scenarios)], 
                       rotation=45, ha='right')
    ax2.grid(True, alpha=0.3)
    
    # Add value labels on bars
    for i, (bar, mean, std) in enumerate(zip(bars2, lr_means, lr_stds)):
        ax2.text(bar.get_x() + bar.get_width()/2, bar.get_height() + std + 0.005,
                f'{mean:.3f}', ha='center', va='bottom', fontsize=9)
    
    plt.tight_layout()
    
    # Save plot
    output_dir = Path("results/feature_validation")
    output_dir.mkdir(parents=True, exist_ok=True)
    plt.savefig(output_dir / 'feature_comparison.png', dpi=300, bbox_inches='tight')
    logger.info(f"Plot saved to: {output_dir / 'feature_comparison.png'}")
    
    plt.show()

def generate_simple_report(results):
    """Generate simple comparison report"""
    
    report = []
    report.append("="*80)
    report.append("SIMPLE FEATURE IMPORTANCE TEST REPORT")
    report.append("="*80)
    
    # Sort by Random Forest performance
    sorted_results = sorted(results.items(), key=lambda x: x[1]['rf_mean'], reverse=True)
    
    report.append(f"\n📊 PERFORMANCE RANKING (by Random Forest accuracy):")
    for i, (scenario, result) in enumerate(sorted_results):
        report.append(f"{i+1:2d}. {scenario} ({result['n_features']} features)")
        report.append(f"    RF: {result['rf_mean']:.4f} ± {result['rf_std']:.4f}")
        report.append(f"    LR: {result['lr_mean']:.4f} ± {result['lr_std']:.4f}")
    
    # Find best performing scenarios
    best_rf = max(results.items(), key=lambda x: x[1]['rf_mean'])
    best_lr = max(results.items(), key=lambda x: x[1]['lr_mean'])
    
    report.append(f"\n🏆 BEST PERFORMANCE:")
    report.append(f"   • Random Forest: {best_rf[0]} - {best_rf[1]['rf_mean']:.4f}")
    report.append(f"   • Logistic Regression: {best_lr[0]} - {best_lr[1]['lr_mean']:.4f}")
    
    # Compare SHAP features vs random features
    report.append(f"\n🎯 SHAP vs RANDOM COMPARISON:")
    
    shap_scenarios = [k for k in results.keys() if 'Consensus' in k or 'RF Features' in k]
    random_scenarios = [k for k in results.keys() if 'Random' in k]
    
    if shap_scenarios and random_scenarios:
        avg_shap_rf = np.mean([results[s]['rf_mean'] for s in shap_scenarios])
        avg_random_rf = np.mean([results[s]['rf_mean'] for s in random_scenarios])
        advantage = avg_shap_rf - avg_random_rf
        
        report.append(f"   • Average SHAP features RF accuracy: {avg_shap_rf:.4f}")
        report.append(f"   • Average Random features RF accuracy: {avg_random_rf:.4f}")
        report.append(f"   • SHAP advantage: {advantage:.4f} ({advantage*100:.2f}%)")
        
        if advantage > 0.02:
            report.append("   ✅ SHAP features show significant advantage")
        elif advantage > 0:
            report.append("   ✅ SHAP features show modest advantage")
        else:
            report.append("   ❌ SHAP features do not show clear advantage")
    
    # Feature efficiency analysis
    report.append(f"\n⚡ FEATURE EFFICIENCY:")
    
    # Find scenarios with good performance but fewer features
    all_features_perf = results.get('All Features', {}).get('rf_mean', 0)
    
    for scenario, result in results.items():
        if scenario != 'All Features' and result['n_features'] < len(results.get('All Features', {}).get('features', [])):
            perf_ratio = result['rf_mean'] / all_features_perf if all_features_perf > 0 else 0
            efficiency = perf_ratio / (result['n_features'] / len(results.get('All Features', {}).get('features', [1])))
            
            report.append(f"   • {scenario}: {perf_ratio:.3f} performance ratio, {efficiency:.3f} efficiency")
    
    # Conclusions
    report.append(f"\n💡 CONCLUSIONS:")
    
    # Check if fewer features can achieve similar performance
    if 'All Features' in results:
        all_perf = results['All Features']['rf_mean']
        threshold = all_perf - 0.02  # Within 2% of all features
        
        efficient_scenarios = [
            (k, v) for k, v in results.items() 
            if k != 'All Features' and v['rf_mean'] >= threshold and v['n_features'] < results['All Features']['n_features']
        ]
        
        if efficient_scenarios:
            best_efficient = max(efficient_scenarios, key=lambda x: x[1]['rf_mean'])
            report.append(f"   ✅ {best_efficient[0]} achieves {best_efficient[1]['rf_mean']:.4f} with only {best_efficient[1]['n_features']} features")
            report.append(f"   ✅ Feature reduction possible without significant performance loss")
        else:
            report.append("   ⚠️ All features seem necessary for optimal performance")
    
    # SHAP validation
    consensus_perf = results.get('Consensus Features', {}).get('rf_mean', 0)
    if consensus_perf > 0:
        if consensus_perf >= all_features_perf - 0.01:
            report.append("   ✅ SHAP consensus features maintain excellent performance")
        elif consensus_perf >= all_features_perf - 0.03:
            report.append("   ✅ SHAP consensus features maintain good performance")
        else:
            report.append("   ⚠️ SHAP consensus features show notable performance drop")
    
    report.append("="*80)
    
    # Save and print report
    report_text = '\n'.join(report)
    
    output_dir = Path("results/feature_validation")
    output_dir.mkdir(parents=True, exist_ok=True)
    report_file = output_dir / 'simple_test_report.txt'
    
    with open(report_file, 'w', encoding='utf-8') as f:
        f.write(report_text)
    
    print(report_text)
    logger.info(f"Report saved to: {report_file}")
    
    return report_text

def main():
    """Main function"""
    
    # Check for data file
    data_path = "dataset/processed/safe_ml_fatigue_dataset.csv"
    
    if not Path(data_path).exists():
        print(f"❌ Data file not found: {data_path}")
        print("Available alternatives:")
        
        # Look for other possible data files
        possible_files = [
            "dataset/processed/safe_ml_bias_corrected_dataset.csv",
            "dataset/processed/safe_ml_title_only_dataset.csv",
            "dataset/processed/fatigue_risk_classified_dataset.csv"
        ]
        
        for file_path in possible_files:
            if Path(file_path).exists():
                print(f"✅ Found: {file_path}")
                data_path = file_path
                break
        else:
            print("❌ No suitable data file found. Please run the main pipeline first.")
            return
    
    # Check for SHAP results
    shap_dir = Path("results/feature_selection")
    if not shap_dir.exists():
        print("❌ SHAP results not found. Please run main1.py first.")
        return
    
    print(f"🚀 Starting simple feature importance test...")
    print(f"📁 Using data: {data_path}")
    
    # Run tests
    results = test_feature_sets(data_path)
    
    if not results:
        print("❌ No results generated. Check your data and feature files.")
        return
    
    # Create visualizations
    create_comparison_plot(results)
    
    # Generate report
    generate_simple_report(results)
    
    print("\n🎉 Simple feature test completed!")
    print("📁 Results saved to: results/feature_validation/")

if __name__ == "__main__":
    main()
