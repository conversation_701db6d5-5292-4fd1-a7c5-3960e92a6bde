#!/usr/bin/env python3
"""
Create research-specific visualizations for cardiovascular activity & productivity study
Focus on visualizations that directly answer research questions
"""

import sys
import pandas as pd
import numpy as np
import matplotlib
matplotlib.use('Agg')
import matplotlib.pyplot as plt
import seaborn as sns
from pathlib import Path
from scipy import stats
from sklearn.linear_model import LinearRegression
from sklearn.metrics import r2_score
import warnings
warnings.filterwarnings('ignore')

sys.path.append('src')

def create_regression_analysis_plot():
    """Create regression analysis plot for cardiovascular activity vs productivity"""
    print("📊 Creating Regression Analysis Plot...")
    
    try:
        # Load data
        data_path = Path("dataset/processed/weekly_merged_dataset_with_gamification.csv")
        if not data_path.exists():
            print("❌ Data file not found")
            return
        
        data = pd.read_csv(data_path)
        
        # Create visualization directory
        viz_dir = Path("results/visualizations")
        viz_dir.mkdir(parents=True, exist_ok=True)
        
        # Create regression analysis
        fig, axes = plt.subplots(2, 2, figsize=(15, 12))
        fig.suptitle('Cardiovascular Activity vs Productivity - Regression Analysis', 
                    fontsize=16, fontweight='bold')
        
        # 1. Distance vs Cycles with regression line
        if 'total_distance_km' in data.columns and 'total_cycles' in data.columns:
            x = data['total_distance_km'].dropna()
            y = data['total_cycles'][x.index]
            
            # Remove outliers (beyond 3 std)
            z_scores = np.abs(stats.zscore(x))
            mask = z_scores < 3
            x_clean = x[mask]
            y_clean = y[mask]
            
            # Regression
            slope, intercept, r_value, p_value, std_err = stats.linregress(x_clean, y_clean)
            line = slope * x_clean + intercept
            
            axes[0, 0].scatter(x_clean, y_clean, alpha=0.6, color='blue')
            axes[0, 0].plot(x_clean, line, 'r-', linewidth=2)
            axes[0, 0].set_title(f'Distance vs Productivity\nR² = {r_value**2:.3f}, p = {p_value:.3f}')
            axes[0, 0].set_xlabel('Weekly Distance (km)')
            axes[0, 0].set_ylabel('Weekly Productivity (cycles)')
            axes[0, 0].grid(True, alpha=0.3)
        
        # 2. Consistency vs Achievement with regression
        if 'consistency_score' in data.columns and 'achievement_rate' in data.columns:
            x = data['consistency_score'].dropna()
            y = data['achievement_rate'][x.index]
            
            slope, intercept, r_value, p_value, std_err = stats.linregress(x, y)
            line = slope * x + intercept
            
            axes[0, 1].scatter(x, y, alpha=0.6, color='green')
            axes[0, 1].plot(x, line, 'r-', linewidth=2)
            axes[0, 1].set_title(f'Consistency vs Achievement\nR² = {r_value**2:.3f}, p = {p_value:.3f}')
            axes[0, 1].set_xlabel('Consistency Score')
            axes[0, 1].set_ylabel('Achievement Rate')
            axes[0, 1].grid(True, alpha=0.3)
        
        # 3. Activity Points vs Productivity Points
        if 'activity_points' in data.columns and 'productivity_points' in data.columns:
            x = data['activity_points'].dropna()
            y = data['productivity_points'][x.index]
            
            slope, intercept, r_value, p_value, std_err = stats.linregress(x, y)
            line = slope * x + intercept
            
            axes[1, 0].scatter(x, y, alpha=0.6, color='orange')
            axes[1, 0].plot(x, line, 'r-', linewidth=2)
            axes[1, 0].set_title(f'Activity Points vs Productivity Points\nR² = {r_value**2:.3f}, p = {p_value:.3f}')
            axes[1, 0].set_xlabel('Activity Points')
            axes[1, 0].set_ylabel('Productivity Points')
            axes[1, 0].grid(True, alpha=0.3)
        
        # 4. Weekly Efficiency Distribution
        if 'weekly_efficiency' in data.columns:
            efficiency_clean = data['weekly_efficiency'].dropna()
            # Remove extreme outliers
            q1, q3 = efficiency_clean.quantile([0.25, 0.75])
            iqr = q3 - q1
            lower_bound = q1 - 1.5 * iqr
            upper_bound = q3 + 1.5 * iqr
            efficiency_clean = efficiency_clean[(efficiency_clean >= lower_bound) & 
                                              (efficiency_clean <= upper_bound)]
            
            axes[1, 1].hist(efficiency_clean, bins=20, alpha=0.7, color='purple', edgecolor='black')
            axes[1, 1].axvline(efficiency_clean.mean(), color='red', linestyle='--', 
                              label=f'Mean: {efficiency_clean.mean():.2f}')
            axes[1, 1].axvline(efficiency_clean.median(), color='orange', linestyle='--', 
                              label=f'Median: {efficiency_clean.median():.2f}')
            axes[1, 1].set_title('Weekly Efficiency Distribution')
            axes[1, 1].set_xlabel('Weekly Efficiency')
            axes[1, 1].set_ylabel('Frequency')
            axes[1, 1].legend()
            axes[1, 1].grid(True, alpha=0.3)
        
        plt.tight_layout()
        
        # Save plot
        plot_path = viz_dir / 'regression_analysis.png'
        plt.savefig(plot_path, dpi=300, bbox_inches='tight')
        print(f"✅ Saved regression analysis plot to {plot_path}")
        
        plt.close()
        
    except Exception as e:
        print(f"❌ Error creating regression analysis: {e}")

def create_intervention_effectiveness_plot():
    """Create intervention effectiveness visualization"""
    print("📊 Creating Intervention Effectiveness Plot...")
    
    try:
        # Load data
        data_path = Path("dataset/processed/weekly_merged_dataset_with_gamification.csv")
        if not data_path.exists():
            print("❌ Data file not found")
            return
        
        data = pd.read_csv(data_path)
        
        # Create visualization directory
        viz_dir = Path("results/visualizations")
        viz_dir.mkdir(parents=True, exist_ok=True)
        
        # Create intervention analysis
        fig, axes = plt.subplots(2, 2, figsize=(15, 10))
        fig.suptitle('Gamification Intervention Effectiveness Analysis', 
                    fontsize=16, fontweight='bold')
        
        # 1. Achievement Rate by Quartiles
        if 'achievement_rate' in data.columns:
            # Create quartiles
            data['achievement_quartile'] = pd.qcut(data['achievement_rate'], 4, 
                                                  labels=['Low', 'Medium-Low', 'Medium-High', 'High'])
            quartile_counts = data['achievement_quartile'].value_counts().sort_index()
            
            colors = ['red', 'orange', 'lightgreen', 'green']
            bars = axes[0, 0].bar(quartile_counts.index, quartile_counts.values, 
                                 color=colors, alpha=0.7)
            axes[0, 0].set_title('Achievement Rate Distribution by Quartiles')
            axes[0, 0].set_xlabel('Achievement Level')
            axes[0, 0].set_ylabel('Number of Users')
            axes[0, 0].grid(True, alpha=0.3)
            
            # Add percentage labels
            total = quartile_counts.sum()
            for bar, count in zip(bars, quartile_counts.values):
                pct = (count / total) * 100
                axes[0, 0].text(bar.get_x() + bar.get_width()/2., bar.get_height(),
                               f'{pct:.1f}%', ha='center', va='bottom')
        
        # 2. Gamification Balance Analysis
        if 'gamification_balance' in data.columns:
            balance_clean = data['gamification_balance'].dropna()
            
            axes[0, 1].hist(balance_clean, bins=20, alpha=0.7, color='skyblue', edgecolor='black')
            axes[0, 1].axvline(0, color='red', linestyle='--', linewidth=2, label='Perfect Balance')
            axes[0, 1].axvline(balance_clean.mean(), color='orange', linestyle='--', 
                              label=f'Mean: {balance_clean.mean():.2f}')
            axes[0, 1].set_title('Gamification Balance Distribution')
            axes[0, 1].set_xlabel('Balance Score (Activity - Productivity)')
            axes[0, 1].set_ylabel('Frequency')
            axes[0, 1].legend()
            axes[0, 1].grid(True, alpha=0.3)
        
        # 3. Effectiveness by User Activity Level
        if 'total_distance_km' in data.columns and 'achievement_rate' in data.columns:
            # Create activity level categories
            data['activity_level'] = pd.cut(data['total_distance_km'], 
                                          bins=3, labels=['Low Activity', 'Medium Activity', 'High Activity'])
            
            activity_achievement = data.groupby('activity_level')['achievement_rate'].agg(['mean', 'std']).reset_index()
            
            bars = axes[1, 0].bar(activity_achievement['activity_level'], activity_achievement['mean'],
                                 yerr=activity_achievement['std'], capsize=5, alpha=0.7, color='purple')
            axes[1, 0].set_title('Achievement Rate by Activity Level')
            axes[1, 0].set_xlabel('Physical Activity Level')
            axes[1, 0].set_ylabel('Average Achievement Rate')
            axes[1, 0].grid(True, alpha=0.3)
            
            # Add value labels
            for bar, mean_val in zip(bars, activity_achievement['mean']):
                axes[1, 0].text(bar.get_x() + bar.get_width()/2., bar.get_height(),
                               f'{mean_val:.3f}', ha='center', va='bottom')
        
        # 4. Consistency vs Productivity Relationship
        if 'consistency_score' in data.columns and 'total_cycles' in data.columns:
            # Create consistency categories
            data['consistency_level'] = pd.cut(data['consistency_score'], 
                                             bins=3, labels=['Low Consistency', 'Medium Consistency', 'High Consistency'])
            
            consistency_productivity = data.groupby('consistency_level')['total_cycles'].agg(['mean', 'std']).reset_index()
            
            bars = axes[1, 1].bar(consistency_productivity['consistency_level'], consistency_productivity['mean'],
                                 yerr=consistency_productivity['std'], capsize=5, alpha=0.7, color='teal')
            axes[1, 1].set_title('Productivity by Consistency Level')
            axes[1, 1].set_xlabel('Consistency Level')
            axes[1, 1].set_ylabel('Average Weekly Cycles')
            axes[1, 1].grid(True, alpha=0.3)
            
            # Add value labels
            for bar, mean_val in zip(bars, consistency_productivity['mean']):
                axes[1, 1].text(bar.get_x() + bar.get_width()/2., bar.get_height(),
                               f'{mean_val:.1f}', ha='center', va='bottom')
        
        plt.tight_layout()
        
        # Save plot
        plot_path = viz_dir / 'intervention_effectiveness.png'
        plt.savefig(plot_path, dpi=300, bbox_inches='tight')
        print(f"✅ Saved intervention effectiveness plot to {plot_path}")
        
        plt.close()
        
    except Exception as e:
        print(f"❌ Error creating intervention effectiveness plot: {e}")

def main():
    """Create research-specific visualizations"""
    print("🎯 CREATING RESEARCH-SPECIFIC VISUALIZATIONS")
    print("=" * 60)
    print("Focus: Cardiovascular Activity & Productivity Research")
    print("=" * 60)
    
    create_regression_analysis_plot()
    create_intervention_effectiveness_plot()
    
    # Check results
    viz_dir = Path("results/visualizations")
    files = list(viz_dir.glob("*.png"))
    
    print(f"\n📊 Total visualization files: {len(files)}")
    print("\n📋 Research-Specific Visualizations:")
    research_files = ['regression_analysis.png', 'intervention_effectiveness.png']
    
    for file in research_files:
        file_path = viz_dir / file
        if file_path.exists():
            size_mb = file_path.stat().st_size / (1024 * 1024)
            print(f"  ✅ {file} ({size_mb:.2f} MB)")
        else:
            print(f"  ❌ {file} (not created)")
    
    print("\n✅ Research-specific visualizations completed!")
    print("🎯 These visualizations directly answer your research questions about")
    print("   cardiovascular activity and productivity relationships.")

if __name__ == "__main__":
    main()
