================================================================================
K-FOLD CROSS-VALIDATION OVERFITTING ANALYSIS REPORT
================================================================================

📊 DATASET INFORMATION:
   • Total samples: 291
   • Features: 20
   • Target distribution: {'medium_risk': np.int64(145), 'high_risk': np.int64(100), 'low_risk': np.int64(46)}
   • K values tested: 2 to 20

🎯 MODEL PERFORMANCE SUMMARY:
   • Logistic Regression:
     - Best validation: 0.6676 ± 0.1417 (k=20)
     - Training at best k: 0.7287
     - Train-val gap: 0.0611
   • Random Forest:
     - Best validation: 0.9553 ± 0.0033 (k=2)
     - Training at best k: 1.0000
     - Train-val gap: 0.0447
   • Gradient Boosting:
     - Best validation: 0.9379 ± 0.0514 (k=15)
     - Training at best k: 1.0000
     - Train-val gap: 0.0621
   • XGBoost:
     - Best validation: 0.9450 ± 0.0175 (k=3)
     - Training at best k: 1.0000
     - Train-val gap: 0.0550

🔍 OVERFITTING ANALYSIS:
   Models ranked by overfitting risk (low to high):
   1. Random Forest - LOW RISK
      • Overfitting score: 4.67
      • Optimal k: 2
      • Max train-val gap: 0.0652
   2. XGBoost - LOW RISK
      • Overfitting score: 5.35
      • Optimal k: 3
      • Max train-val gap: 0.0723
   3. Gradient Boosting - LOW RISK
      • Overfitting score: 6.26
      • Optimal k: 3
      • Max train-val gap: 0.0928
   4. Logistic Regression - MEDIUM RISK
      • Overfitting score: 11.98
      • Optimal k: 2
      • Max train-val gap: 0.1220

💡 RECOMMENDATIONS:
   • BEST MODEL: Random Forest (lowest overfitting risk)
   • RECOMMENDED K: 2
   • AVERAGE OPTIMAL K: 2.5
   • Low k values suggest small dataset - consider data augmentation
================================================================================