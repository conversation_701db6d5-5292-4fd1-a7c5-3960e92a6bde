"""
SHAP-Based Optimal Features - Generated 20250721_023144
Best Algorithm: Random Forest (Accuracy: 0.9492)
"""

# All features ranked by SHAP importance
SHAP_RANKED_FEATURES = [
    "consistency_score",  # SHAP: 0.2601
    "work_days",  # SHAP: 0.1227
    "gamification_balance",  # SHAP: 0.0239
    "total_distance_km",  # SHAP: 0.0163
    "title_balance_ratio",  # SHAP: 0.0148
    "strava_title_count",  # SHAP: 0.0140
    "pomokit_title_length",  # SHAP: 0.0135
    "strava_title_length",  # SHAP: 0.0101
    "total_time_minutes",  # SHAP: 0.0084
    "strava_unique_words",  # SHAP: 0.0082
    "avg_time_minutes",  # SHAP: 0.0056
    "avg_distance_km",  # SHAP: 0.0028
    "achievement_rate",  # SHAP: 0.0015
    "pomokit_unique_words",  # SHAP: 0.0008
    "pomokit_title_count",  # SHAP: 0.0003
    "total_title_diversity",  # SHAP: 0.0002
    "total_cycles",  # SHAP: 0.0002
    "activity_points",  # SHAP: 0.0000
    "productivity_points",  # SHAP: 0.0000
    "activity_days",  # SHAP: 0.0000
]

# Top 5 SHAP features
TOP_5_SHAP_FEATURES = [
    "consistency_score",  # SHAP: 0.2601
    "work_days",  # SHAP: 0.1227
    "gamification_balance",  # SHAP: 0.0239
    "total_distance_km",  # SHAP: 0.0163
    "title_balance_ratio",  # SHAP: 0.0148
]

# Top 10 SHAP features
TOP_10_SHAP_FEATURES = [
    "consistency_score",  # SHAP: 0.2601
    "work_days",  # SHAP: 0.1227
    "gamification_balance",  # SHAP: 0.0239
    "total_distance_km",  # SHAP: 0.0163
    "title_balance_ratio",  # SHAP: 0.0148
    "strava_title_count",  # SHAP: 0.0140
    "pomokit_title_length",  # SHAP: 0.0135
    "strava_title_length",  # SHAP: 0.0101
    "total_time_minutes",  # SHAP: 0.0084
    "strava_unique_words",  # SHAP: 0.0082
]

# Top 15 SHAP features
TOP_15_SHAP_FEATURES = [
    "consistency_score",  # SHAP: 0.2601
    "work_days",  # SHAP: 0.1227
    "gamification_balance",  # SHAP: 0.0239
    "total_distance_km",  # SHAP: 0.0163
    "title_balance_ratio",  # SHAP: 0.0148
    "strava_title_count",  # SHAP: 0.0140
    "pomokit_title_length",  # SHAP: 0.0135
    "strava_title_length",  # SHAP: 0.0101
    "total_time_minutes",  # SHAP: 0.0084
    "strava_unique_words",  # SHAP: 0.0082
    "avg_time_minutes",  # SHAP: 0.0056
    "avg_distance_km",  # SHAP: 0.0028
    "achievement_rate",  # SHAP: 0.0015
    "pomokit_unique_words",  # SHAP: 0.0008
    "pomokit_title_count",  # SHAP: 0.0003
]

# SHAP importance scores
SHAP_IMPORTANCE_SCORES = {
    "consistency_score": 0.260066,
    "work_days": 0.122738,
    "gamification_balance": 0.023862,
    "total_distance_km": 0.016277,
    "title_balance_ratio": 0.014835,
    "strava_title_count": 0.014006,
    "pomokit_title_length": 0.013535,
    "strava_title_length": 0.010061,
    "total_time_minutes": 0.008426,
    "strava_unique_words": 0.008167,
    "avg_time_minutes": 0.005591,
    "avg_distance_km": 0.002831,
    "achievement_rate": 0.001529,
    "pomokit_unique_words": 0.000842,
    "pomokit_title_count": 0.000260,
    "total_title_diversity": 0.000218,
    "total_cycles": 0.000204,
    "activity_points": 0.000014,
    "productivity_points": 0.000000,
    "activity_days": 0.000000,
}

# Usage example:
# X_shap = X[TOP_5_SHAP_FEATURES]
# model.fit(X_shap, y)