================================================================================
SIMPLE FEATURE IMPORTANCE TEST REPORT
================================================================================

📊 PERFORMANCE RANKING (by Random Forest accuracy):
 1. Top 3 Consensus (3 features)
    RF: 0.9554 ± 0.0318
    LR: 0.6050 ± 0.0485
 2. Top 10 RF Features (10 features)
    RF: 0.9417 ± 0.0336
    LR: 0.6117 ± 0.0637
 3. Consensus Features (10 features)
    RF: 0.9348 ± 0.0396
    LR: 0.6117 ± 0.0637
 4. All Features (20 features)
    RF: 0.9348 ± 0.0333
    LR: 0.6292 ± 0.0674
 5. Random 10 Features (10 features)
    RF: 0.9314 ± 0.0305
    LR: 0.5638 ± 0.0620
 6. Top 5 Consensus (5 features)
    RF: 0.9279 ± 0.0315
    LR: 0.6325 ± 0.0547
 7. Random 5 Features (5 features)
    RF: 0.9141 ± 0.0362
    LR: 0.5844 ± 0.0651

🏆 BEST PERFORMANCE:
   • Random Forest: Top 3 Consensus - 0.9554
   • Logistic Regression: Top 5 Consensus - 0.6325

🎯 SHAP vs RANDOM COMPARISON:
   • Average SHAP features RF accuracy: 0.9399
   • Average Random features RF accuracy: 0.9227
   • SHAP advantage: 0.0172 (1.72%)
   ✅ SHAP features show modest advantage

⚡ FEATURE EFFICIENCY:
   • Consensus Features: 1.000 performance ratio, 2.000 efficiency
   • Top 10 RF Features: 1.007 performance ratio, 2.015 efficiency
   • Top 5 Consensus: 0.993 performance ratio, 3.970 efficiency
   • Top 3 Consensus: 1.022 performance ratio, 6.814 efficiency
   • Random 10 Features: 0.996 performance ratio, 1.993 efficiency
   • Random 5 Features: 0.978 performance ratio, 3.911 efficiency

💡 CONCLUSIONS:
   ✅ Top 3 Consensus achieves 0.9554 with only 3 features
   ✅ Feature reduction possible without significant performance loss
   ✅ SHAP consensus features maintain excellent performance
================================================================================