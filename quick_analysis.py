#!/usr/bin/env python3
"""
Quick Analysis Script
Demonstrates how to perform quick data analysis and visualization
using the built-in visualization features in data_processor.py
"""

import sys
import os
sys.path.append('src')

from data_processor import DataProcessor
from constants import DataConstants
import pandas as pd

def quick_data_overview():
    """Quick overview of the dataset with key visualizations"""
    print("🔍 QUICK DATA ANALYSIS")
    print("=" * 50)
    
    processor = DataProcessor()
    
    # Check if processed data exists
    processed_file = processor.processed_data_path / DataConstants.OUTPUT_FILENAME
    
    if processed_file.exists():
        print("✓ Loading existing processed data...")
        data = pd.read_csv(processed_file)
    else:
        print("⚠️  No processed data found. Processing raw data...")
        data = processor.process_all()
    
    print(f"📊 Dataset: {data.shape[0]} observations, {data.shape[1]} features")
    print(f"👥 Users: {data['identity'].nunique()} unique users")
    
    # Quick data quality check
    print("\n🔍 QUICK DATA QUALITY CHECK")
    print("-" * 30)
    missing_data = data.isnull().sum().sum()
    print(f"Missing values: {missing_data}")
    
    # Key statistics
    print(f"Average weekly distance: {data['total_distance_km'].mean():.2f} km")
    print(f"Average weekly cycles: {data['total_cycles'].mean():.1f}")
    print(f"Average consistency score: {data['consistency_score'].mean():.3f}")
    print(f"Average achievement rate: {data['achievement_rate'].mean():.3f}")
    
    # Create essential visualizations
    print("\n📈 CREATING VISUALIZATIONS")
    print("-" * 30)
    
    print("1. Data overview...")
    processor.visualize_data_overview(data, save_plots=True)
    
    print("2. User analysis...")
    processor.visualize_user_analysis(data, save_plots=True)
    
    print("3. Gamification analysis...")
    processor.visualize_gamification_analysis(data, save_plots=True)
    
    print("\n✅ Quick analysis completed!")
    print("📁 Check 'results/visualizations/' for visualization files")

def analyze_top_performers():
    """Analyze top performing users in detail"""
    print("\n🏆 TOP PERFORMERS ANALYSIS")
    print("=" * 50)
    
    processor = DataProcessor()
    processed_file = processor.processed_data_path / DataConstants.OUTPUT_FILENAME
    
    if processed_file.exists():
        data = pd.read_csv(processed_file)
    else:
        data = processor.process_all()
    
    # Find top performers
    top_distance = data.groupby('identity')['total_distance_km'].sum().nlargest(5)
    top_cycles = data.groupby('identity')['total_cycles'].sum().nlargest(5)
    top_consistency = data.groupby('identity')['consistency_score'].mean().nlargest(5)
    
    print("🏃 TOP 5 BY DISTANCE:")
    for i, (user, distance) in enumerate(top_distance.items(), 1):
        print(f"  {i}. {user}: {distance:.1f} km")
    
    print("\n💼 TOP 5 BY PRODUCTIVITY:")
    for i, (user, cycles) in enumerate(top_cycles.items(), 1):
        print(f"  {i}. {user}: {cycles:.0f} cycles")
    
    print("\n🎯 TOP 5 BY CONSISTENCY:")
    for i, (user, score) in enumerate(top_consistency.items(), 1):
        print(f"  {i}. {user}: {score:.3f} score")

def analyze_trends():
    """Analyze trends over time"""
    print("\n📈 TREND ANALYSIS")
    print("=" * 50)
    
    processor = DataProcessor()
    processed_file = processor.processed_data_path / DataConstants.OUTPUT_FILENAME
    
    if processed_file.exists():
        data = pd.read_csv(processed_file)
    else:
        data = processor.process_all()
    
    # Weekly trends
    weekly_trends = data.groupby('year_week').agg({
        'total_distance_km': 'mean',
        'total_cycles': 'mean',
        'consistency_score': 'mean',
        'achievement_rate': 'mean'
    }).round(2)
    
    print("📊 WEEKLY TRENDS (Average values):")
    print(weekly_trends)
    
    # Create time series visualization
    print("\n📈 Creating time series visualization...")
    processor.visualize_time_series(data, save_plots=True)
    
    # Trend insights
    distance_trend = weekly_trends['total_distance_km'].diff().mean()
    cycles_trend = weekly_trends['total_cycles'].diff().mean()
    
    print(f"\n🔍 TREND INSIGHTS:")
    print(f"Distance trend: {'📈 Increasing' if distance_trend > 0 else '📉 Decreasing'} ({distance_trend:.2f} km/week)")
    print(f"Productivity trend: {'📈 Increasing' if cycles_trend > 0 else '📉 Decreasing'} ({cycles_trend:.2f} cycles/week)")

def main():
    """Main function to run quick analysis"""
    try:
        # Run quick analysis
        quick_data_overview()
        analyze_top_performers()
        analyze_trends()
        
        print("\n" + "=" * 60)
        print("🎉 QUICK ANALYSIS COMPLETED!")
        print("=" * 60)
        print("📁 Generated files:")
        print("  - results/visualizations/data_overview.png")
        print("  - results/visualizations/user_analysis.png")
        print("  - results/visualizations/gamification_analysis.png")
        print("  - results/visualizations/time_series_analysis.png")
        
    except Exception as e:
        print(f"❌ Error during analysis: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
