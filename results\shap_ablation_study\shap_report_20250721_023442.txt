================================================================================
🔍 SHAP ABLATION STUDY REPORT
================================================================================

📋 DATASET INFORMATION:
   • Dataset Path: dataset/processed/safe_ml_fatigue_dataset.csv
   • Target Column: fatigue_risk
   • Total Features: 20
   • Total Samples: 291
   • Target Distribution: {'medium_risk': 145, 'high_risk': 100, 'low_risk': 46}

🤖 MODEL PERFORMANCE:
   • Logistic Regression:
     - Test Accuracy: 0.6441
     - Test F1-Score: 0.6412
     - CV Accuracy: 0.6764 (±0.0526)
     - CV F1-Score: 0.6587 (±0.0560)
   • Random Forest:
     - Test Accuracy: 0.9492
     - Test F1-Score: 0.9520
     - CV Accuracy: 0.9567 (±0.0238)
     - CV F1-Score: 0.9574 (±0.0232)
   • Gradient Boosting:
     - Test Accuracy: 0.9492
     - Test F1-Score: 0.9520
     - CV Accuracy: 0.9568 (±0.0236)
     - CV F1-Score: 0.9574 (±0.0234)
   • XGBoost:
     - Test Accuracy: 0.9322
     - Test F1-Score: 0.9333
     - CV Accuracy: 0.9351 (±0.0364)
     - CV F1-Score: 0.9342 (±0.0369)

🎯 GLOBAL SHAP FEATURE IMPORTANCE:

   📊 Logistic Regression:
      1. 🔥 consistency_score: 0.2481
      2. ⭐ total_cycles: 0.0887
      3. ⭐ gamification_balance: 0.0537
      4. ▫️ total_distance_km: 0.0182
      5. ▫️ title_balance_ratio: 0.0163
      6. ▫️ activity_days: 0.0161
      7. ▫️ pomokit_title_length: 0.0131
      8. ▫️ total_time_minutes: 0.0088
      9. ▫️ strava_unique_words: 0.0083
     10. ▫️ strava_title_length: 0.0073

   📊 Random Forest:
      1. 🔥 consistency_score: 0.2475
      2. ⭐ total_cycles: 0.0883
      3. ⭐ gamification_balance: 0.0530
      4. ▫️ title_balance_ratio: 0.0174
      5. ▫️ total_distance_km: 0.0169
      6. ▫️ activity_days: 0.0156
      7. ▫️ pomokit_title_length: 0.0133
      8. ▫️ total_time_minutes: 0.0107
      9. ▫️ strava_unique_words: 0.0086
     10. ▫️ strava_title_length: 0.0072

   📊 Gradient Boosting:
      1. 🔥 consistency_score: 0.2479
      2. ⭐ total_cycles: 0.0883
      3. ⭐ gamification_balance: 0.0537
      4. ▫️ title_balance_ratio: 0.0176
      5. ▫️ total_distance_km: 0.0163
      6. ▫️ activity_days: 0.0160
      7. ▫️ pomokit_title_length: 0.0133
      8. ▫️ total_time_minutes: 0.0111
      9. ▫️ strava_unique_words: 0.0088
     10. ▫️ strava_title_length: 0.0078

   📊 XGBoost:
      1. 🔥 consistency_score: 0.2475
      2. ⭐ total_cycles: 0.0882
      3. ⭐ gamification_balance: 0.0531
      4. ▫️ title_balance_ratio: 0.0174
      5. ▫️ total_distance_km: 0.0163
      6. ▫️ activity_days: 0.0156
      7. ▫️ pomokit_title_length: 0.0131
      8. ▫️ total_time_minutes: 0.0111
      9. ▫️ strava_unique_words: 0.0087
     10. ▫️ strava_title_length: 0.0076

🔬 SHAP ANALYSIS ADVANTAGES:
   • Theoretically grounded (Shapley values from game theory)
   • Individual prediction explanations available
   • Captures feature interactions
   • Model-agnostic interpretability
   • Positive/negative contribution analysis

🎖️ FEATURE CONSISTENCY ACROSS ALGORITHMS:
   Most consistent features (appearing in multiple top-5 lists):
     • consistency_score: 4/4 algorithms (100.0%)
     • total_cycles: 4/4 algorithms (100.0%)
     • gamification_balance: 4/4 algorithms (100.0%)
     • total_distance_km: 4/4 algorithms (100.0%)
     • title_balance_ratio: 4/4 algorithms (100.0%)

✅ SHAP-BASED RECOMMENDATIONS:
   • Best performing algorithm: Random Forest (0.9492 accuracy)
   • Top 5 SHAP features for production:
     1. consistency_score (SHAP: 0.2475)
     2. total_cycles (SHAP: 0.0883)
     3. gamification_balance (SHAP: 0.0530)
     4. title_balance_ratio (SHAP: 0.0174)
     5. total_distance_km (SHAP: 0.0169)

📊 SHAP ANALYSIS SUMMARY:
   • Total algorithms analyzed: 4
   • Total features analyzed: 20
   • Best accuracy achieved: 0.9492
   • Analysis timestamp: 2025-07-21T02:34:42.010114