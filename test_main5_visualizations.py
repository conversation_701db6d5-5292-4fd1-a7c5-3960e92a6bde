#!/usr/bin/env python3
"""
Test script to verify that main5.py now saves visualizations to results/visualizations/
"""

import sys
import os
from pathlib import Path
import subprocess
import time

def check_visualization_directory():
    """Check current state of visualization directory"""
    viz_dir = Path("results/visualizations")
    
    if not viz_dir.exists():
        print("📁 Creating results/visualizations directory...")
        viz_dir.mkdir(parents=True, exist_ok=True)
        return []
    
    files = list(viz_dir.glob("*.png"))
    return [f.name for f in files]

def test_main5_with_visualizations():
    """Test main5.py to see if it creates visualizations"""
    print("🧪 TESTING: python main5.py --mode bias-corrected-only")
    print("=" * 60)
    
    before_files = check_visualization_directory()
    print(f"📊 Files before: {len(before_files)}")
    
    if before_files:
        print("📋 Existing files:")
        for file in sorted(before_files):
            print(f"  • {file}")
    
    try:
        # Run main5.py in bias-corrected-only mode (faster)
        print("\n⚙️ Running main5.py...")
        result = subprocess.run([
            sys.executable, "main5.py", "--mode", "bias-corrected-only"
        ], capture_output=True, text=True, timeout=300)  # 5 minutes timeout
        
        print(f"📋 Return code: {result.returncode}")
        
        if result.returncode == 0:
            print("✅ main5.py executed successfully")
        else:
            print(f"❌ main5.py failed with return code {result.returncode}")
            if result.stderr:
                print(f"Error output: {result.stderr[:500]}...")
        
        # Show some output
        if result.stdout:
            lines = result.stdout.split('\n')
            print(f"\n📄 Output (last 10 lines):")
            for line in lines[-10:]:
                if line.strip():
                    print(f"  {line}")
        
        # Check files after
        after_files = check_visualization_directory()
        new_files = set(after_files) - set(before_files)
        
        print(f"\n📊 Files after: {len(after_files)}")
        print(f"📈 New files: {len(new_files)}")
        
        if new_files:
            print("🆕 New visualization files:")
            for file in sorted(new_files):
                file_path = Path("results/visualizations") / file
                size_mb = file_path.stat().st_size / (1024 * 1024)
                print(f"  ✅ {file} ({size_mb:.2f} MB)")
        else:
            print("⚠️  No new visualization files created")
        
        return len(new_files) > 0, after_files
        
    except subprocess.TimeoutExpired:
        print("⏰ main5.py timed out (5 minutes)")
        return False, []
    except Exception as e:
        print(f"❌ Error running main5.py: {e}")
        return False, []

def analyze_expected_visualizations():
    """Analyze what visualizations should be created by main5.py"""
    print("\n📊 EXPECTED VISUALIZATIONS from main5.py:")
    print("-" * 50)
    
    expected_files = [
        ('data_overview.png', 'Data processing overview'),
        ('correlation_matrix.png', 'Correlation analysis'),
        ('time_series_analysis.png', 'Time series trends'),
        ('user_analysis.png', 'User performance analysis'),
        ('gamification_analysis.png', 'Gamification metrics'),
        ('classification_analysis.png', 'Classification results'),
        ('feature_analysis.png', 'Feature importance'),
        ('feature_filtering_analysis.png', 'Feature filtering results'),
        ('bias_correction_impact.png', 'Bias correction impact'),
        ('research_specific_analysis.png', 'Research-specific analysis (NEW)')
    ]
    
    viz_dir = Path("results/visualizations")
    
    for filename, description in expected_files:
        file_path = viz_dir / filename
        if file_path.exists():
            size_mb = file_path.stat().st_size / (1024 * 1024)
            print(f"  ✅ {filename} ({size_mb:.2f} MB) - {description}")
        else:
            print(f"  ❌ {filename} - {description}")

def main():
    """Main test function"""
    print("🧪 TESTING main5.py VISUALIZATION CAPABILITIES")
    print("=" * 80)
    print("Testing whether main5.py now saves visualizations to results/visualizations/")
    print("=" * 80)
    
    # Show expected visualizations
    analyze_expected_visualizations()
    
    # Test main5.py
    success, final_files = test_main5_with_visualizations()
    
    # Final analysis
    print("\n" + "=" * 80)
    print("🎯 FINAL ANALYSIS")
    print("=" * 80)
    
    if success:
        print("✅ SUCCESS: main5.py now creates visualizations!")
        print(f"📊 Total visualization files: {len(final_files)}")
        
        print("\n📋 All visualization files in results/visualizations/:")
        for file in sorted(final_files):
            file_path = Path("results/visualizations") / file
            size_mb = file_path.stat().st_size / (1024 * 1024)
            print(f"  📊 {file} ({size_mb:.2f} MB)")
        
        print(f"\n🎉 CONCLUSION: main5.py successfully saves visualizations to results/visualizations/")
        
    else:
        print("❌ ISSUE: main5.py did not create new visualizations")
        print("🔍 Possible reasons:")
        print("  • Script failed to execute")
        print("  • Visualizations already existed")
        print("  • Error in visualization code")
        
        if final_files:
            print(f"\n📊 Existing files ({len(final_files)}):")
            for file in sorted(final_files):
                print(f"  📊 {file}")
    
    print("\n✅ Testing completed!")

if __name__ == "__main__":
    main()
